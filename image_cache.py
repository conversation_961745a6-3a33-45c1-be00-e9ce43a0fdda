"""
Comprehensive image caching system for the Image Crawler application.
Provides multi-level caching for thumbnails, full images, metadata, and processed images.
"""

import os
import hashlib
import pickle
import threading
import time
import tempfile
import shutil
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path
from PIL import Image
import logging
from collections import OrderedDict
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta


@dataclass
class ImageMetadata:
    """Metadata for cached images"""
    file_path: str
    file_size: int
    file_mtime: float
    original_size: Tuple[int, int]
    format: str
    mode: str
    has_transparency: bool
    created_at: float
    accessed_at: float
    access_count: int = 0


@dataclass
class CacheEntry:
    """Cache entry with metadata and data"""
    key: str
    data: Any
    metadata: ImageMetadata
    cache_type: str
    size_bytes: int
    created_at: float
    accessed_at: float
    access_count: int = 0


class LRUCache:
    """Thread-safe LRU cache implementation"""
    
    def __init__(self, max_size: int, max_memory_mb: int = 100):
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.cache = OrderedDict()
        self.current_memory = 0
        self.lock = threading.RLock()
        self.hits = 0
        self.misses = 0
    
    def get(self, key: str) -> Optional[CacheEntry]:
        """Get item from cache"""
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                # Move to end (most recently used)
                self.cache.move_to_end(key)
                entry.accessed_at = time.time()
                entry.access_count += 1
                self.hits += 1
                return entry
            else:
                self.misses += 1
                return None
    
    def put(self, key: str, entry: CacheEntry):
        """Put item in cache"""
        with self.lock:
            # Remove existing entry if present
            if key in self.cache:
                old_entry = self.cache[key]
                self.current_memory -= old_entry.size_bytes
                del self.cache[key]
            
            # Add new entry
            self.cache[key] = entry
            self.current_memory += entry.size_bytes
            
            # Evict if necessary
            self._evict_if_needed()
    
    def _evict_if_needed(self):
        """Evict least recently used items if cache is full"""
        # Evict by count
        while len(self.cache) > self.max_size:
            self._evict_oldest()
        
        # Evict by memory
        while self.current_memory > self.max_memory_bytes and self.cache:
            self._evict_oldest()
    
    def _evict_oldest(self):
        """Evict the oldest (least recently used) item"""
        if self.cache:
            key, entry = self.cache.popitem(last=False)
            self.current_memory -= entry.size_bytes
    
    def clear(self):
        """Clear all cache entries"""
        with self.lock:
            self.cache.clear()
            self.current_memory = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            total_requests = self.hits + self.misses
            hit_rate = (self.hits / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'memory_mb': self.current_memory / (1024 * 1024),
                'max_memory_mb': self.max_memory_bytes / (1024 * 1024),
                'hits': self.hits,
                'misses': self.misses,
                'hit_rate': hit_rate
            }


class ImageCache:
    """Comprehensive image caching system"""
    
    def __init__(self, cache_dir: Optional[str] = None, 
                 thumbnail_cache_size: int = 500,
                 image_cache_size: int = 50,
                 metadata_cache_size: int = 1000,
                 max_memory_mb: int = 200):
        
        # Cache directory setup
        if cache_dir is None:
            cache_dir = os.path.join(tempfile.gettempdir(), 'image_crawler_cache')
        
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Subdirectories for different cache types
        self.thumbnail_dir = self.cache_dir / 'thumbnails'
        self.image_dir = self.cache_dir / 'images'
        self.metadata_dir = self.cache_dir / 'metadata'
        
        for dir_path in [self.thumbnail_dir, self.image_dir, self.metadata_dir]:
            dir_path.mkdir(exist_ok=True)
        
        # Memory caches
        self.thumbnail_cache = LRUCache(thumbnail_cache_size, max_memory_mb // 2)
        self.image_cache = LRUCache(image_cache_size, max_memory_mb // 2)
        self.metadata_cache = LRUCache(metadata_cache_size, 10)  # Metadata is small
        
        # Cache settings
        self.max_memory_mb = max_memory_mb
        self.cache_enabled = True
        
        # Statistics
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'disk_reads': 0,
            'disk_writes': 0,
            'cache_evictions': 0
        }
        
        # Cleanup old cache files on startup
        self._cleanup_old_cache_files()
    
    def _get_file_key(self, file_path: str, extra_params: str = "") -> str:
        """Generate cache key for file"""
        # Include file path, modification time, and extra parameters
        try:
            mtime = os.path.getmtime(file_path)
            key_string = f"{file_path}:{mtime}:{extra_params}"
            return hashlib.md5(key_string.encode()).hexdigest()
        except OSError:
            # File doesn't exist, use path only
            key_string = f"{file_path}:{extra_params}"
            return hashlib.md5(key_string.encode()).hexdigest()
    
    def _get_image_metadata(self, file_path: str) -> Optional[ImageMetadata]:
        """Get or create image metadata"""
        if not os.path.exists(file_path):
            return None
        
        key = self._get_file_key(file_path, "metadata")
        
        # Check memory cache first
        cached_entry = self.metadata_cache.get(key)
        if cached_entry:
            return cached_entry.data
        
        try:
            # Load metadata from file
            stat = os.stat(file_path)
            
            # Try to load image to get metadata
            with Image.open(file_path) as img:
                metadata = ImageMetadata(
                    file_path=file_path,
                    file_size=stat.st_size,
                    file_mtime=stat.st_mtime,
                    original_size=img.size,
                    format=img.format or 'Unknown',
                    mode=img.mode,
                    has_transparency=img.mode in ('RGBA', 'LA') or 'transparency' in img.info,
                    created_at=time.time(),
                    accessed_at=time.time()
                )
            
            # Cache metadata
            entry = CacheEntry(
                key=key,
                data=metadata,
                metadata=metadata,
                cache_type='metadata',
                size_bytes=len(pickle.dumps(metadata)),
                created_at=time.time(),
                accessed_at=time.time()
            )
            
            self.metadata_cache.put(key, entry)
            return metadata
            
        except Exception as e:
            logging.warning(f"Failed to get metadata for {file_path}: {e}")
            return None
    
    def get_thumbnail(self, file_path: str, size: int = 150, 
                     quality: int = 85) -> Optional[Image.Image]:
        """Get cached thumbnail or create new one"""
        if not self.cache_enabled or not os.path.exists(file_path):
            return None
        
        key = self._get_file_key(file_path, f"thumb_{size}_{quality}")
        
        # Check memory cache first
        cached_entry = self.thumbnail_cache.get(key)
        if cached_entry:
            self.stats['cache_hits'] += 1
            return cached_entry.data.copy()  # Return copy to prevent modification
        
        # Check disk cache
        cache_file = self.thumbnail_dir / f"{key}.jpg"
        if cache_file.exists():
            try:
                thumbnail = Image.open(cache_file)
                
                # Add to memory cache
                metadata = self._get_image_metadata(file_path)
                if metadata:
                    entry = CacheEntry(
                        key=key,
                        data=thumbnail.copy(),
                        metadata=metadata,
                        cache_type='thumbnail',
                        size_bytes=cache_file.stat().st_size,
                        created_at=time.time(),
                        accessed_at=time.time()
                    )
                    self.thumbnail_cache.put(key, entry)
                
                self.stats['cache_hits'] += 1
                return thumbnail.copy()
                
            except Exception as e:
                logging.warning(f"Failed to load cached thumbnail {cache_file}: {e}")
                # Remove corrupted cache file
                cache_file.unlink(missing_ok=True)
        
        # Create new thumbnail
        self.stats['cache_misses'] += 1
        return self._create_and_cache_thumbnail(file_path, size, quality, key)
    
    def _create_and_cache_thumbnail(self, file_path: str, size: int, 
                                   quality: int, key: str) -> Optional[Image.Image]:
        """Create thumbnail and cache it"""
        try:
            with Image.open(file_path) as img:
                # Create thumbnail
                thumbnail = img.copy()
                thumbnail.thumbnail((size, size), Image.Resampling.LANCZOS)
                
                # Convert to RGB if necessary for JPEG saving
                if thumbnail.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', thumbnail.size, (255, 255, 255))
                    if thumbnail.mode == 'RGBA':
                        background.paste(thumbnail, mask=thumbnail.split()[3])
                    else:
                        background.paste(thumbnail, mask=thumbnail.split()[1])
                    thumbnail = background
                
                # Save to disk cache
                cache_file = self.thumbnail_dir / f"{key}.jpg"
                thumbnail.save(cache_file, 'JPEG', quality=quality, optimize=True)
                self.stats['disk_writes'] += 1
                
                # Add to memory cache
                metadata = self._get_image_metadata(file_path)
                if metadata:
                    entry = CacheEntry(
                        key=key,
                        data=thumbnail.copy(),
                        metadata=metadata,
                        cache_type='thumbnail',
                        size_bytes=cache_file.stat().st_size,
                        created_at=time.time(),
                        accessed_at=time.time()
                    )
                    self.thumbnail_cache.put(key, entry)
                
                return thumbnail.copy()
                
        except Exception as e:
            logging.error(f"Failed to create thumbnail for {file_path}: {e}")
            return None

    def get_full_image(self, file_path: str, max_size: Tuple[int, int] = None) -> Optional[Image.Image]:
        """Get cached full image or load from disk"""
        if not self.cache_enabled or not os.path.exists(file_path):
            return None

        max_size_str = f"{max_size[0]}x{max_size[1]}" if max_size else "original"
        key = self._get_file_key(file_path, f"full_{max_size_str}")

        # Check memory cache first
        cached_entry = self.image_cache.get(key)
        if cached_entry:
            self.stats['cache_hits'] += 1
            return cached_entry.data.copy()

        # Load from disk
        try:
            self.stats['disk_reads'] += 1
            with Image.open(file_path) as img:
                # Apply size limit if specified
                if max_size and (img.width > max_size[0] or img.height > max_size[1]):
                    img.thumbnail(max_size, Image.Resampling.LANCZOS)

                # Convert RGBA to RGB if necessary
                if img.mode == 'RGBA':
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[3])
                    img = background

                # Cache in memory
                metadata = self._get_image_metadata(file_path)
                if metadata:
                    # Estimate memory size
                    memory_size = img.width * img.height * len(img.getbands())

                    entry = CacheEntry(
                        key=key,
                        data=img.copy(),
                        metadata=metadata,
                        cache_type='full_image',
                        size_bytes=memory_size,
                        created_at=time.time(),
                        accessed_at=time.time()
                    )
                    self.image_cache.put(key, entry)

                return img.copy()

        except Exception as e:
            logging.error(f"Failed to load image {file_path}: {e}")
            return None

    def cache_processed_image(self, original_path: str, processed_image: Image.Image,
                            operation: str, params: str = "") -> str:
        """Cache a processed image and return cache key"""
        if not self.cache_enabled:
            return ""

        key = self._get_file_key(original_path, f"processed_{operation}_{params}")

        try:
            # Save to disk cache
            cache_file = self.image_dir / f"{key}.png"
            processed_image.save(cache_file, 'PNG', optimize=True)
            self.stats['disk_writes'] += 1

            # Add to memory cache
            metadata = self._get_image_metadata(original_path)
            if metadata:
                memory_size = processed_image.width * processed_image.height * len(processed_image.getbands())

                entry = CacheEntry(
                    key=key,
                    data=processed_image.copy(),
                    metadata=metadata,
                    cache_type='processed',
                    size_bytes=memory_size,
                    created_at=time.time(),
                    accessed_at=time.time()
                )
                self.image_cache.put(key, entry)

            return key

        except Exception as e:
            logging.error(f"Failed to cache processed image: {e}")
            return ""

    def get_processed_image(self, original_path: str, operation: str,
                          params: str = "") -> Optional[Image.Image]:
        """Get cached processed image"""
        if not self.cache_enabled:
            return None

        key = self._get_file_key(original_path, f"processed_{operation}_{params}")

        # Check memory cache first
        cached_entry = self.image_cache.get(key)
        if cached_entry:
            self.stats['cache_hits'] += 1
            return cached_entry.data.copy()

        # Check disk cache
        cache_file = self.image_dir / f"{key}.png"
        if cache_file.exists():
            try:
                processed_image = Image.open(cache_file)

                # Add to memory cache
                metadata = self._get_image_metadata(original_path)
                if metadata:
                    memory_size = processed_image.width * processed_image.height * len(processed_image.getbands())

                    entry = CacheEntry(
                        key=key,
                        data=processed_image.copy(),
                        metadata=metadata,
                        cache_type='processed',
                        size_bytes=memory_size,
                        created_at=time.time(),
                        accessed_at=time.time()
                    )
                    self.image_cache.put(key, entry)

                self.stats['cache_hits'] += 1
                return processed_image.copy()

            except Exception as e:
                logging.warning(f"Failed to load cached processed image {cache_file}: {e}")
                cache_file.unlink(missing_ok=True)

        self.stats['cache_misses'] += 1
        return None

    def invalidate_file(self, file_path: str):
        """Invalidate all cache entries for a file"""
        # This is a simplified approach - in practice, we'd need to track keys by file
        # For now, we'll clear the entire cache when files change significantly
        pass

    def clear_cache(self, cache_type: str = None):
        """Clear cache entries"""
        if cache_type == 'thumbnails' or cache_type is None:
            self.thumbnail_cache.clear()
            shutil.rmtree(self.thumbnail_dir, ignore_errors=True)
            self.thumbnail_dir.mkdir(exist_ok=True)

        if cache_type == 'images' or cache_type is None:
            self.image_cache.clear()
            shutil.rmtree(self.image_dir, ignore_errors=True)
            self.image_dir.mkdir(exist_ok=True)

        if cache_type == 'metadata' or cache_type is None:
            self.metadata_cache.clear()
            shutil.rmtree(self.metadata_dir, ignore_errors=True)
            self.metadata_dir.mkdir(exist_ok=True)

    def _cleanup_old_cache_files(self, max_age_days: int = 7):
        """Clean up old cache files"""
        cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)

        for cache_dir in [self.thumbnail_dir, self.image_dir, self.metadata_dir]:
            if cache_dir.exists():
                for cache_file in cache_dir.iterdir():
                    try:
                        if cache_file.stat().st_mtime < cutoff_time:
                            cache_file.unlink()
                    except OSError:
                        pass  # File might have been deleted by another process

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        thumbnail_stats = self.thumbnail_cache.get_stats()
        image_stats = self.image_cache.get_stats()
        metadata_stats = self.metadata_cache.get_stats()

        # Calculate disk usage
        disk_usage = 0
        for cache_dir in [self.thumbnail_dir, self.image_dir, self.metadata_dir]:
            if cache_dir.exists():
                for cache_file in cache_dir.iterdir():
                    try:
                        disk_usage += cache_file.stat().st_size
                    except OSError:
                        pass

        return {
            'enabled': self.cache_enabled,
            'disk_usage_mb': disk_usage / (1024 * 1024),
            'thumbnail_cache': thumbnail_stats,
            'image_cache': image_stats,
            'metadata_cache': metadata_stats,
            'total_stats': self.stats,
            'cache_directory': str(self.cache_dir)
        }

    def set_enabled(self, enabled: bool):
        """Enable or disable caching"""
        self.cache_enabled = enabled

    def optimize_cache(self):
        """Optimize cache by removing least used items"""
        # This could be expanded to implement more sophisticated optimization
        # For now, the LRU caches handle this automatically
        pass


# Global cache instance
_global_cache = None
_cache_lock = threading.Lock()


def get_image_cache() -> ImageCache:
    """Get global image cache instance"""
    global _global_cache

    if _global_cache is None:
        with _cache_lock:
            if _global_cache is None:
                _global_cache = ImageCache()

    return _global_cache


def configure_cache(cache_dir: str = None, max_memory_mb: int = 200,
                   thumbnail_cache_size: int = 500, image_cache_size: int = 50):
    """Configure global cache settings"""
    global _global_cache

    with _cache_lock:
        _global_cache = ImageCache(
            cache_dir=cache_dir,
            max_memory_mb=max_memory_mb,
            thumbnail_cache_size=thumbnail_cache_size,
            image_cache_size=image_cache_size
        )
