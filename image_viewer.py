import customtkinter as ctk
import tkinter as tk
from PIL import Image, ImageTk, ImageOps, ImageEnhance, ImageFilter
import os
import shutil
import platform
from tkinter import messagebox, filedialog
import traceback
from image_cache import get_image_cache

class ImageViewer(ctk.CTkToplevel):
    def __init__(self, parent, image_path, image_list=None, current_index=None):
        super().__init__(parent)
        
        self.parent = parent
        self.image_path = image_path
        self.image_list = image_list or [image_path]
        self.current_index = current_index if current_index is not None else 0
        self.original_image = None
        self.current_image = None
        self.photo = None
        self.scale_factor = 1.0
        self.rotation = 0
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.ico'}
        self.max_image_size = (4096, 4096)  # Maximum dimensions for performance
        self.cache = get_image_cache()
        
        # Window setup
        self.title(f"Image Viewer - {os.path.basename(image_path)}")
        self.geometry("1000x700")
        self.minsize(800, 600)
        
        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        # Create UI elements
        self.create_toolbar()
        self.create_image_canvas()
        self.create_info_panel()
        
        # Load and display image
        self.load_image()
        
        # Bind keyboard shortcuts
        self.bind_shortcuts()
        
        # Focus window
        self.lift()
        self.focus_force()
        
    def create_toolbar(self):
        """Create the toolbar with controls"""
        self.toolbar = ctk.CTkFrame(self, height=50)
        self.toolbar.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        
        # Zoom controls
        self.zoom_frame = ctk.CTkFrame(self.toolbar, fg_color="transparent")
        self.zoom_frame.pack(side="left", padx=10)
        
        self.zoom_out_btn = ctk.CTkButton(
            self.zoom_frame,
            text="➖",
            width=30,
            height=30,
            command=self.zoom_out
        )
        self.zoom_out_btn.pack(side="left", padx=2)
        
        self.zoom_label = ctk.CTkLabel(self.zoom_frame, text="100%", width=60)
        self.zoom_label.pack(side="left", padx=5)
        
        self.zoom_in_btn = ctk.CTkButton(
            self.zoom_frame,
            text="➕",
            width=30,
            height=30,
            command=self.zoom_in
        )
        self.zoom_in_btn.pack(side="left", padx=2)
        
        self.fit_btn = ctk.CTkButton(
            self.zoom_frame,
            text="Fit",
            width=50,
            height=30,
            command=self.fit_to_window
        )
        self.fit_btn.pack(side="left", padx=(10, 2))
        
        self.actual_size_btn = ctk.CTkButton(
            self.zoom_frame,
            text="1:1",
            width=50,
            height=30,
            command=self.actual_size
        )
        self.actual_size_btn.pack(side="left", padx=2)
        
        # Separator
        self.sep1 = ctk.CTkFrame(self.toolbar, width=2, fg_color="gray")
        self.sep1.pack(side="left", fill="y", padx=10)
        
        # Rotation controls
        self.rotation_frame = ctk.CTkFrame(self.toolbar, fg_color="transparent")
        self.rotation_frame.pack(side="left", padx=10)
        
        self.rotate_left_btn = ctk.CTkButton(
            self.rotation_frame,
            text="↺",
            width=30,
            height=30,
            command=self.rotate_left
        )
        self.rotate_left_btn.pack(side="left", padx=2)
        
        self.rotate_right_btn = ctk.CTkButton(
            self.rotation_frame,
            text="↻",
            width=30,
            height=30,
            command=self.rotate_right
        )
        self.rotate_right_btn.pack(side="left", padx=2)
        
        self.flip_h_btn = ctk.CTkButton(
            self.rotation_frame,
            text="↔",
            width=30,
            height=30,
            command=self.flip_horizontal
        )
        self.flip_h_btn.pack(side="left", padx=2)
        
        self.flip_v_btn = ctk.CTkButton(
            self.rotation_frame,
            text="↕",
            width=30,
            height=30,
            command=self.flip_vertical
        )
        self.flip_v_btn.pack(side="left", padx=2)
        
        # Separator
        self.sep2 = ctk.CTkFrame(self.toolbar, width=2, fg_color="gray")
        self.sep2.pack(side="left", fill="y", padx=10)
        
        # Enhancement controls
        self.enhance_frame = ctk.CTkFrame(self.toolbar, fg_color="transparent")
        self.enhance_frame.pack(side="left", padx=10)
        
        self.enhance_btn = ctk.CTkButton(
            self.enhance_frame,
            text="✨ Enhance",
            width=80,
            height=30,
            command=self.toggle_enhancements
        )
        self.enhance_btn.pack(side="left", padx=2)
        
        # Navigation buttons (if multiple images)
        if len(self.image_list) > 1:
            self.sep3 = ctk.CTkFrame(self.toolbar, width=2, fg_color="gray")
            self.sep3.pack(side="left", fill="y", padx=10)
            
            self.nav_frame = ctk.CTkFrame(self.toolbar, fg_color="transparent")
            self.nav_frame.pack(side="left", padx=10)
            
            self.prev_btn = ctk.CTkButton(
                self.nav_frame,
                text="◀",
                width=30,
                height=30,
                command=self.previous_image
            )
            self.prev_btn.pack(side="left", padx=2)
            
            self.nav_label = ctk.CTkLabel(
                self.nav_frame,
                text=f"{self.current_index + 1}/{len(self.image_list)}",
                width=60
            )
            self.nav_label.pack(side="left", padx=5)
            
            self.next_btn = ctk.CTkButton(
                self.nav_frame,
                text="▶",
                width=30,
                height=30,
                command=self.next_image
            )
            self.next_btn.pack(side="left", padx=2)
        
        # Action buttons (right side)
        self.action_frame = ctk.CTkFrame(self.toolbar, fg_color="transparent")
        self.action_frame.pack(side="right", padx=10)
        
        self.save_as_btn = ctk.CTkButton(
            self.action_frame,
            text="💾 Save As",
            width=80,
            height=30,
            command=self.save_as
        )
        self.save_as_btn.pack(side="left", padx=2)
        
        self.copy_btn = ctk.CTkButton(
            self.action_frame,
            text="📋 Copy",
            width=80,
            height=30,
            command=self.copy_to_clipboard
        )
        self.copy_btn.pack(side="left", padx=2)
        
        self.delete_btn = ctk.CTkButton(
            self.action_frame,
            text="🗑️ Delete",
            width=80,
            height=30,
            fg_color="red",
            hover_color="darkred",
            command=self.delete_image
        )
        self.delete_btn.pack(side="left", padx=2)
        
    def create_image_canvas(self):
        """Create the canvas for displaying the image"""
        self.canvas_frame = ctk.CTkFrame(self)
        self.canvas_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
        
        self.canvas = tk.Canvas(self.canvas_frame, bg="black", highlightthickness=0)
        self.canvas.pack(fill="both", expand=True)
        
        # Scrollbars
        self.h_scroll = ctk.CTkScrollbar(self.canvas_frame, orientation="horizontal", command=self.canvas.xview)
        self.v_scroll = ctk.CTkScrollbar(self.canvas_frame, orientation="vertical", command=self.canvas.yview)
        
        self.canvas.configure(xscrollcommand=self.h_scroll.set, yscrollcommand=self.v_scroll.set)
        
        # Bind mouse events
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<B1-Motion>", self.on_canvas_drag)
        self.canvas.bind("<MouseWheel>", self.on_mouse_wheel)
        self.canvas.bind("<Control-MouseWheel>", self.on_ctrl_mouse_wheel)
        
        # Variables for panning
        self.drag_start_x = 0
        self.drag_start_y = 0
        
    def create_info_panel(self):
        """Create the information panel"""
        self.info_panel = ctk.CTkFrame(self, height=80)
        self.info_panel.grid(row=2, column=0, sticky="ew", padx=5, pady=5)
        self.info_panel.grid_propagate(False)
        
        # Image information
        self.info_frame = ctk.CTkFrame(self.info_panel, fg_color="transparent")
        self.info_frame.pack(fill="x", padx=10, pady=10)
        
        self.filename_label = ctk.CTkLabel(
            self.info_frame,
            text=f"File: {os.path.basename(self.image_path)}",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.filename_label.pack(anchor="w")
        
        self.details_frame = ctk.CTkFrame(self.info_frame, fg_color="transparent")
        self.details_frame.pack(fill="x", pady=(5, 0))
        
        self.size_label = ctk.CTkLabel(self.details_frame, text="Size: -")
        self.size_label.pack(side="left", padx=(0, 20))
        
        self.dimensions_label = ctk.CTkLabel(self.details_frame, text="Dimensions: -")
        self.dimensions_label.pack(side="left", padx=(0, 20))
        
        self.format_label = ctk.CTkLabel(self.details_frame, text="Format: -")
        self.format_label.pack(side="left", padx=(0, 20))
        
        self.mode_label = ctk.CTkLabel(self.details_frame, text="Mode: -")
        self.mode_label.pack(side="left")
        
    def show_error_message(self, message, suggestion=None):
        full_message = message
        if suggestion:
            full_message += f"\n\nSuggestion: {suggestion}"
        messagebox.showerror("Error", full_message)
        
    def load_image(self):
        """Load and display the image"""
        try:
            # Check if file exists
            if not os.path.exists(self.image_path):
                raise FileNotFoundError(f"Image file not found: {self.image_path}")
            
            # Check file extension
            ext = os.path.splitext(self.image_path)[1].lower()
            if ext not in self.supported_formats:
                raise ValueError(f"Unsupported image format: {ext}")
            
            # Try to get cached image first
            cached_image = self.cache.get_full_image(self.image_path, self.max_image_size)

            if cached_image:
                self.original_image = cached_image
            else:
                # Load image from disk
                self.original_image = Image.open(self.image_path)

                # Convert RGBA to RGB if necessary for certain operations
                if self.original_image.mode == 'RGBA':
                    # Create a white background
                    background = Image.new('RGB', self.original_image.size, (255, 255, 255))
                    background.paste(self.original_image, mask=self.original_image.split()[3])
                    self.original_image = background

                # Check image size and resize if too large
                if (self.original_image.width > self.max_image_size[0] or
                    self.original_image.height > self.max_image_size[1]):
                    self.original_image.thumbnail(self.max_image_size, Image.Resampling.LANCZOS)
                    messagebox.showwarning("Large Image",
                        "Image was resized for performance. Original dimensions were preserved.")
            
            self.current_image = self.original_image.copy()
            
            # Update window title
            self.title(f"Image Viewer - {os.path.basename(self.image_path)} ({self.current_index + 1}/{len(self.image_list)})")
            
            # Update info panel
            self.update_info()
            
            # Display image
            self.fit_to_window()
            
        except Exception as e:
            error_msg = f"Failed to load image: {str(e)}\n\nDetails:\n{traceback.format_exc()}"
            suggestion = None
            if isinstance(e, FileNotFoundError):
                suggestion = "Check if the file was moved or deleted."
            elif "format" in str(e).lower():
                suggestion = "Supported formats: jpg, png, gif, bmp, webp, tiff, ico."
            self.show_error_message(error_msg, suggestion)
            self.destroy()
            
    def update_info(self):
        """Update the information panel"""
        if self.original_image:
            # File size
            file_size = os.path.getsize(self.image_path)
            size_str = self.format_size(file_size)
            self.size_label.configure(text=f"Size: {size_str}")
            
            # Dimensions
            width, height = self.original_image.size
            self.dimensions_label.configure(text=f"Dimensions: {width} × {height}")
            
            # Format
            format_str = self.original_image.format or "Unknown"
            self.format_label.configure(text=f"Format: {format_str}")
            
            # Mode
            mode_str = self.original_image.mode
            self.mode_label.configure(text=f"Mode: {mode_str}")
            
    def display_image(self):
        """Display the current image on canvas"""
        if not self.current_image:
            return
            
        # Calculate display size
        display_width = int(self.current_image.width * self.scale_factor)
        display_height = int(self.current_image.height * self.scale_factor)
        
        # Resize image for display
        display_image = self.current_image.resize(
            (display_width, display_height),
            Image.Resampling.LANCZOS
        )
        
        # Convert to PhotoImage
        self.photo = ImageTk.PhotoImage(display_image)
        
        # Clear canvas
        self.canvas.delete("all")
        
        # Update canvas scroll region
        self.canvas.configure(scrollregion=(0, 0, display_width, display_height))
        
        # Display image
        self.canvas.create_image(0, 0, anchor="nw", image=self.photo)
        
        # Update zoom label
        self.zoom_label.configure(text=f"{int(self.scale_factor * 100)}%")
        
        # Show/hide scrollbars as needed
        self.update_scrollbars()
        
    def update_scrollbars(self):
        """Show or hide scrollbars based on image size"""
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if self.current_image:
            image_width = int(self.current_image.width * self.scale_factor)
            image_height = int(self.current_image.height * self.scale_factor)
            
            if image_width > canvas_width:
                self.h_scroll.pack(side="bottom", fill="x")
            else:
                self.h_scroll.pack_forget()
                
            if image_height > canvas_height:
                self.v_scroll.pack(side="right", fill="y")
            else:
                self.v_scroll.pack_forget()
                
    def zoom_in(self):
        """Zoom in the image"""
        self.scale_factor *= 1.2
        if self.scale_factor > 5.0:
            self.scale_factor = 5.0
        self.display_image()
        
    def zoom_out(self):
        """Zoom out the image"""
        self.scale_factor /= 1.2
        if self.scale_factor < 0.1:
            self.scale_factor = 0.1
        self.display_image()
        
    def fit_to_window(self):
        """Fit image to window size"""
        if not self.current_image:
            return
            
        self.update()  # Ensure canvas dimensions are updated
        
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width > 1 and canvas_height > 1:
            # Calculate scale to fit
            scale_x = canvas_width / self.current_image.width
            scale_y = canvas_height / self.current_image.height
            self.scale_factor = min(scale_x, scale_y) * 0.95  # 95% to add padding
            
            self.display_image()
            
    def actual_size(self):
        """Display image at actual size (1:1)"""
        self.scale_factor = 1.0
        self.display_image()
        
    def rotate_left(self):
        """Rotate image 90 degrees counter-clockwise"""
        if self.current_image:
            self.rotation = (self.rotation + 90) % 360
            self._apply_rotation()

    def rotate_right(self):
        """Rotate image 90 degrees clockwise"""
        if self.current_image:
            self.rotation = (self.rotation - 90) % 360
            self._apply_rotation()

    def _apply_rotation(self):
        """Apply current rotation with caching"""
        if not self.original_image:
            return

        # Check cache for rotated image
        cache_key = f"rotation_{self.rotation}"
        cached_rotated = self.cache.get_processed_image(self.image_path, "rotate", cache_key)

        if cached_rotated:
            self.current_image = cached_rotated
        else:
            # Create rotated image and cache it
            if self.rotation == 0:
                self.current_image = self.original_image.copy()
            else:
                self.current_image = self.original_image.rotate(self.rotation, expand=True)
                self.cache.cache_processed_image(self.image_path, self.current_image, "rotate", cache_key)

        self.display_image()
            
    def flip_horizontal(self):
        """Flip image horizontally"""
        if self.current_image:
            self.current_image = ImageOps.mirror(self.current_image)
            self.display_image()
            
    def flip_vertical(self):
        """Flip image vertically"""
        if self.current_image:
            self.current_image = ImageOps.flip(self.current_image)
            self.display_image()
            
    def toggle_enhancements(self):
        """Toggle image enhancements"""
        if not self.original_image:
            return
            
        # Create enhancement window
        enhance_window = ctk.CTkToplevel(self)
        enhance_window.title("Image Enhancements")
        enhance_window.geometry("400x500")
        enhance_window.transient(self)
        
        # Enhancement controls
        controls_frame = ctk.CTkFrame(enhance_window)
        controls_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Brightness
        bright_label = ctk.CTkLabel(controls_frame, text="Brightness")
        bright_label.pack(pady=(10, 5))
        # CTkSlider expects int for from_ and to, so use 0 and 2, and scale value in lambda
        bright_slider = ctk.CTkSlider(
            controls_frame,
            from_=0,
            to=2,
            command=lambda v: self.apply_enhancement('brightness', float(v))
        )
        bright_slider.set(1)
        bright_slider.pack(fill="x", padx=20)
        
        # Contrast
        contrast_label = ctk.CTkLabel(controls_frame, text="Contrast")
        contrast_label.pack(pady=(20, 5))
        
        contrast_slider = ctk.CTkSlider(
            controls_frame,
            from_=0,
            to=2,
            command=lambda v: self.apply_enhancement('contrast', float(v))
        )
        contrast_slider.set(1)
        contrast_slider.pack(fill="x", padx=20)
        
        # Saturation
        saturation_label = ctk.CTkLabel(controls_frame, text="Saturation")
        saturation_label.pack(pady=(20, 5))
        
        saturation_slider = ctk.CTkSlider(
            controls_frame,
            from_=0,
            to=2,
            command=lambda v: self.apply_enhancement('saturation', float(v))
        )
        saturation_slider.set(1)
        saturation_slider.pack(fill="x", padx=20)
        
        # Sharpness
        sharpness_label = ctk.CTkLabel(controls_frame, text="Sharpness")
        sharpness_label.pack(pady=(20, 5))
        
        sharpness_slider = ctk.CTkSlider(
            controls_frame,
            from_=0,
            to=2,
            command=lambda v: self.apply_enhancement('sharpness', float(v))
        )
        sharpness_slider.set(1)
        sharpness_slider.pack(fill="x", padx=20)
        
        # Buttons
        button_frame = ctk.CTkFrame(controls_frame, fg_color="transparent")
        button_frame.pack(pady=30)
        
        reset_btn = ctk.CTkButton(
            button_frame,
            text="Reset",
            command=lambda: self.reset_image()
        )
        reset_btn.pack(side="left", padx=5)
        
        apply_btn = ctk.CTkButton(
            button_frame,
            text="Apply",
            command=enhance_window.destroy
        )
        apply_btn.pack(side="left", padx=5)
        
    def apply_enhancement(self, enhancement_type, value):
        """Apply enhancement to image with caching"""
        if not self.original_image:
            return

        # Create cache key including rotation and enhancement
        cache_key = f"{enhancement_type}_{value}_rot_{self.rotation}"

        # Check cache first
        cached_enhanced = self.cache.get_processed_image(self.image_path, "enhance", cache_key)

        if cached_enhanced:
            self.current_image = cached_enhanced
        else:
            # Start with original image
            enhanced = self.original_image.copy()

            # Apply rotation if any
            if self.rotation != 0:
                enhanced = enhanced.rotate(self.rotation, expand=True)

            # Apply enhancement
            if enhancement_type == 'brightness':
                enhancer = ImageEnhance.Brightness(enhanced)
                enhanced = enhancer.enhance(value)
            elif enhancement_type == 'contrast':
                enhancer = ImageEnhance.Contrast(enhanced)
                enhanced = enhancer.enhance(value)
            elif enhancement_type == 'saturation':
                enhancer = ImageEnhance.Color(enhanced)
                enhanced = enhancer.enhance(value)
            elif enhancement_type == 'sharpness':
                enhancer = ImageEnhance.Sharpness(enhanced)
                enhanced = enhancer.enhance(value)

            # Cache the enhanced image
            self.cache.cache_processed_image(self.image_path, enhanced, "enhance", cache_key)
            self.current_image = enhanced

        self.display_image()
        
    def reset_image(self):
        """Reset image to original"""
        if self.original_image is None:
            self.show_error_message("No image loaded to reset.")
            return
        self.current_image = self.original_image.copy()
        self.rotation = 0
        self.display_image()
        
    def save_as(self):
        """Save image with a new name"""
        if self.current_image is None:
            self.show_error_message("No image loaded to save.")
            return
            
        filename = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg"),
                ("All files", "*.*")
            ]
        )
        
        if filename:
            try:
                self.current_image.save(filename)
                messagebox.showinfo("Success", "Image saved successfully!")
            except Exception as e:
                suggestion = None
                if "permission" in str(e).lower():
                    suggestion = "Check file permissions or try a different location."
                elif "disk" in str(e).lower() or "space" in str(e).lower():
                    suggestion = "Check available disk space."
                self.show_error_message(f"Failed to save image: {str(e)}", suggestion)
                
    def copy_to_clipboard(self):
        """Copy image to clipboard"""
        if not self.current_image:
            return
            
        system = platform.system()
        
        try:
            if system == "Windows":
                # Windows-specific clipboard handling
                try:
                    import io
                    import win32clipboard
                    
                    output = io.BytesIO()
                    self.current_image.convert("RGB").save(output, "BMP")
                    data = output.getvalue()[14:]  # Remove BMP header
                    output.close()
                    
                    win32clipboard.OpenClipboard()
                    win32clipboard.EmptyClipboard()
                    win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
                    win32clipboard.CloseClipboard()
                    
                    messagebox.showinfo("Success", "Image copied to clipboard!")
                except ImportError:
                    # Fallback: save to temp file
                    self._copy_via_temp_file()
                    
            elif system == "Darwin":  # macOS
                # Use pbcopy command
                import tempfile
                import subprocess
                
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
                    self.current_image.save(tmp.name, 'PNG')
                    subprocess.run(['osascript', '-e', f'set the clipboard to (read (POSIX file "{tmp.name}") as PNG picture)'])
                    os.unlink(tmp.name)
                    
                messagebox.showinfo("Success", "Image copied to clipboard!")
                
            else:  # Linux
                # Use xclip if available
                import subprocess
                import tempfile
                
                try:
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
                        self.current_image.save(tmp.name, 'PNG')
                        subprocess.run(['xclip', '-selection', 'clipboard', '-t', 'image/png', '-i', tmp.name])
                        os.unlink(tmp.name)
                        
                    messagebox.showinfo("Success", "Image copied to clipboard!")
                except FileNotFoundError:
                    messagebox.showwarning("Warning", "xclip not found. Please install xclip to copy images to clipboard.")
                    
        except Exception as e:
            suggestion = None
            if "clipboard" in str(e).lower():
                suggestion = "Try restarting your computer or check clipboard permissions."
            self.show_error_message(f"Failed to copy to clipboard: {str(e)}", suggestion)
    
    def _copy_via_temp_file(self):
        """Fallback method: save to temp file and notify user"""
        import tempfile
        temp_dir = tempfile.gettempdir()
        temp_path = os.path.join(temp_dir, f"temp_image_{os.getpid()}.png")
        if self.current_image is None:
            self.show_error_message("No image loaded to copy.")
            return
        self.current_image.save(temp_path, 'PNG')
        messagebox.showinfo("Image Saved", f"Image saved to:\n{temp_path}\n\nYou can copy it manually.")
            
    def delete_image(self):
        """Delete the current image"""
        if messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this image?"):
            try:
                self.destroy()
                os.remove(self.image_path)
            except Exception as e:
                suggestion = None
                if "permission" in str(e).lower():
                    suggestion = "Check file permissions or try running as administrator."
                self.show_error_message(f"Failed to delete image: {str(e)}", suggestion)
                
    def on_canvas_click(self, event):
        """Handle canvas click for panning"""
        self.drag_start_x = event.x
        self.drag_start_y = event.y
        self.canvas.config(cursor="fleur")  # Change cursor to indicate dragging
        
    def on_canvas_drag(self, event):
        """Handle canvas drag for panning"""
        dx = event.x - self.drag_start_x
        dy = event.y - self.drag_start_y
        
        self.canvas.xview_scroll(-dx, "units")
        self.canvas.yview_scroll(-dy, "units")
        
        self.drag_start_x = event.x
        self.drag_start_y = event.y
        
    def on_mouse_wheel(self, event):
        """Handle mouse wheel for vertical scrolling"""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        
    def on_ctrl_mouse_wheel(self, event):
        """Handle Ctrl+mouse wheel for zooming"""
        if event.delta > 0:
            self.zoom_in()
        else:
            self.zoom_out()
            
    def bind_shortcuts(self):
        """Bind keyboard shortcuts"""
        self.bind("<Control-plus>", lambda e: self.zoom_in())
        self.bind("<Control-minus>", lambda e: self.zoom_out())
        self.bind("<Control-0>", lambda e: self.actual_size())
        self.bind("<Control-f>", lambda e: self.fit_to_window())
        self.bind("<Control-s>", lambda e: self.save_as())
        self.bind("<Control-c>", lambda e: self.copy_to_clipboard())
        self.bind("<Delete>", lambda e: self.delete_image())
        self.bind("<r>", lambda e: self.rotate_left())
        self.bind("<R>", lambda e: self.rotate_right())
        self.bind("<h>", lambda e: self.flip_horizontal())
        self.bind("<v>", lambda e: self.flip_vertical())
        self.bind("<Escape>", lambda e: self.destroy())
        
        # Navigation shortcuts
        if len(self.image_list) > 1:
            self.bind("<Left>", lambda e: self.previous_image())
            self.bind("<Right>", lambda e: self.next_image())
            self.bind("<Page_Up>", lambda e: self.previous_image())
            self.bind("<Page_Down>", lambda e: self.next_image())
        
    def format_size(self, size_bytes):
        """Format file size in human-readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"

    def previous_image(self):
        """Navigate to the previous image in the list"""
        if len(self.image_list) > 1:
            self.current_index = (self.current_index - 1) % len(self.image_list)
            self.image_path = self.image_list[self.current_index]
            self.load_image()
            self.update_navigation_ui()

    def next_image(self):
        """Navigate to the next image in the list"""
        if len(self.image_list) > 1:
            self.current_index = (self.current_index + 1) % len(self.image_list)
            self.image_path = self.image_list[self.current_index]
            self.load_image()
            self.update_navigation_ui()

    def update_navigation_ui(self):
        """Update navigation UI elements"""
        if hasattr(self, 'nav_label') and len(self.image_list) > 1:
            self.nav_label.configure(text=f"{self.current_index + 1}/{len(self.image_list)}")

        # Update window title
        self.title(f"Image Viewer - {os.path.basename(self.image_path)} ({self.current_index + 1}/{len(self.image_list)})")

        # Update filename label
        self.filename_label.configure(text=f"File: {os.path.basename(self.image_path)}")

        # Enable/disable navigation buttons based on position
        if hasattr(self, 'prev_btn'):
            self.prev_btn.configure(state="normal")
        if hasattr(self, 'next_btn'):
            self.next_btn.configure(state="normal")
