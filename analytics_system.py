"""
Advanced analytics and statistics system for the Image Crawler application.
Provides detailed insights about crawling performance, image analysis, and trends.
"""

import os
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter
import threading
from pathlib import Path
from PIL import Image
import customtkinter as ctk
from tkinter import messagebox


class CrawlSession:
    """Represents a single crawling session"""

    def __init__(self, session_id: str, start_url: str, start_time: datetime):
        self.session_id = session_id
        self.start_url = start_url
        self.start_time = start_time
        self.end_time: Optional[datetime] = None
        self.images_found = 0
        self.images_downloaded = 0
        self.images_failed = 0
        self.total_size = 0
        self.domains_crawled = set()
        self.file_types = Counter()
        self.errors = []
        
    def to_dict(self) -> Dict:
        """Convert session to dictionary"""
        return {
            'session_id': self.session_id,
            'start_url': self.start_url,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'images_found': self.images_found,
            'images_downloaded': self.images_downloaded,
            'images_failed': self.images_failed,
            'total_size': self.total_size,
            'domains_crawled': list(self.domains_crawled),
            'file_types': dict(self.file_types),
            'errors': self.errors
        }


class AnalyticsDatabase:
    """SQLite database for storing analytics data"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.init_database()
        
    def init_database(self):
        """Initialize database tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Sessions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    start_url TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    images_found INTEGER,
                    images_downloaded INTEGER,
                    images_failed INTEGER,
                    total_size INTEGER,
                    domains_crawled TEXT,
                    file_types TEXT,
                    errors TEXT
                )
            ''')
            
            # Images table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS images (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    url TEXT,
                    local_path TEXT,
                    file_size INTEGER,
                    width INTEGER,
                    height INTEGER,
                    format TEXT,
                    download_time TEXT,
                    success BOOLEAN,
                    error_message TEXT,
                    FOREIGN KEY (session_id) REFERENCES sessions (session_id)
                )
            ''')
            
            # Performance metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    timestamp TEXT,
                    metric_name TEXT,
                    metric_value REAL,
                    FOREIGN KEY (session_id) REFERENCES sessions (session_id)
                )
            ''')
            
            conn.commit()
            
    def save_session(self, session: CrawlSession):
        """Save crawl session to database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO sessions
                (session_id, start_url, start_time, end_time, images_found,
                 images_downloaded, images_failed, total_size, domains_crawled,
                 file_types, errors)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                session.session_id,
                session.start_url,
                session.start_time.isoformat(),
                session.end_time.isoformat() if session.end_time else "",
                session.images_found,
                session.images_downloaded,
                session.images_failed,
                session.total_size,
                json.dumps(list(session.domains_crawled)),
                json.dumps(dict(session.file_types)),
                json.dumps(session.errors)
            ))
            
            conn.commit()
            
    def save_image_record(self, session_id: str, image_data: Dict):
        """Save individual image record"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO images 
                (session_id, url, local_path, file_size, width, height, 
                 format, download_time, success, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                session_id,
                image_data.get('url', ''),
                image_data.get('local_path', ''),
                image_data.get('file_size', 0),
                image_data.get('width', 0),
                image_data.get('height', 0),
                image_data.get('format', ''),
                image_data.get('download_time', ''),
                image_data.get('success', False),
                image_data.get('error_message', '')
            ))
            
            conn.commit()
            
    def get_sessions(self, limit: int = 50) -> List[Dict]:
        """Get recent crawl sessions"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM sessions 
                ORDER BY start_time DESC 
                LIMIT ?
            ''', (limit,))
            
            columns = [desc[0] for desc in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
            
    def get_performance_stats(self, days: int = 30) -> Dict:
        """Get performance statistics for the last N days"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # Total statistics
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_sessions,
                    SUM(images_found) as total_found,
                    SUM(images_downloaded) as total_downloaded,
                    SUM(images_failed) as total_failed,
                    SUM(total_size) as total_size,
                    AVG(images_downloaded) as avg_downloaded_per_session
                FROM sessions 
                WHERE start_time >= ?
            ''', (cutoff_date,))
            
            stats = dict(zip([desc[0] for desc in cursor.description], cursor.fetchone()))
            
            # File type distribution
            cursor.execute('''
                SELECT file_types FROM sessions 
                WHERE start_time >= ? AND file_types IS NOT NULL
            ''', (cutoff_date,))
            
            file_type_counter = Counter()
            for row in cursor.fetchall():
                if row[0]:
                    types = json.loads(row[0])
                    file_type_counter.update(types)
                    
            stats['file_type_distribution'] = dict(file_type_counter)
            
            return stats


class AnalyticsManager:
    """Main analytics manager"""
    
    def __init__(self, db_path: Optional[str] = None):
        if db_path is None:
            db_path = os.path.join(os.path.expanduser('~'), '.image_crawler', 'analytics.db')
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
        self.db = AnalyticsDatabase(db_path)
        self.current_session = None
        self.lock = threading.Lock()
        
    def start_session(self, start_url: str) -> str:
        """Start a new crawling session"""
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        with self.lock:
            self.current_session = CrawlSession(session_id, start_url, datetime.now())
            
        return session_id
        
    def end_session(self):
        """End the current session"""
        with self.lock:
            if self.current_session:
                self.current_session.end_time = datetime.now()
                self.db.save_session(self.current_session)
                self.current_session = None
                
    def record_image_found(self, url: str, domain: Optional[str] = None):
        """Record that an image was found"""
        with self.lock:
            if self.current_session:
                self.current_session.images_found += 1
                if domain:
                    self.current_session.domains_crawled.add(domain)

    def record_image_downloaded(self, url: str, local_path: str, file_size: int,
                               file_type: str, image_info: Optional[Dict] = None):
        """Record successful image download"""
        with self.lock:
            if self.current_session:
                self.current_session.images_downloaded += 1
                self.current_session.total_size += file_size
                self.current_session.file_types[file_type.upper()] += 1
                
                # Save detailed image record
                image_data = {
                    'url': url,
                    'local_path': local_path,
                    'file_size': file_size,
                    'format': file_type,
                    'download_time': datetime.now().isoformat(),
                    'success': True
                }
                
                if image_info:
                    image_data.update(image_info)

                self.db.save_image_record(self.current_session.session_id, image_data)
                
    def record_image_failed(self, url: str, error_message: str):
        """Record failed image download"""
        with self.lock:
            if self.current_session:
                self.current_session.images_failed += 1
                self.current_session.errors.append({
                    'url': url,
                    'error': error_message,
                    'timestamp': datetime.now().isoformat()
                })
                
                # Save failed image record
                image_data = {
                    'url': url,
                    'download_time': datetime.now().isoformat(),
                    'success': False,
                    'error_message': error_message
                }
                
                self.db.save_image_record(self.current_session.session_id, image_data)
                
    def get_current_session_stats(self) -> Dict:
        """Get statistics for current session"""
        with self.lock:
            if self.current_session:
                return {
                    'session_id': self.current_session.session_id,
                    'start_url': self.current_session.start_url,
                    'duration': (datetime.now() - self.current_session.start_time).total_seconds(),
                    'images_found': self.current_session.images_found,
                    'images_downloaded': self.current_session.images_downloaded,
                    'images_failed': self.current_session.images_failed,
                    'success_rate': (self.current_session.images_downloaded / 
                                   max(1, self.current_session.images_found)) * 100,
                    'total_size': self.current_session.total_size,
                    'domains_crawled': len(self.current_session.domains_crawled),
                    'file_types': dict(self.current_session.file_types)
                }
        return {}
        
    def get_historical_stats(self, days: int = 30) -> Dict:
        """Get historical statistics"""
        return self.db.get_performance_stats(days)
        
    def get_recent_sessions(self, limit: int = 10) -> List[Dict]:
        """Get recent crawling sessions"""
        return self.db.get_sessions(limit)
        
    def analyze_image_collection(self, image_paths: List[str]) -> Dict:
        """Analyze a collection of images"""
        stats = {
            'total_images': len(image_paths),
            'total_size': 0,
            'formats': Counter(),
            'dimensions': [],
            'size_distribution': {'small': 0, 'medium': 0, 'large': 0, 'xlarge': 0},
            'aspect_ratios': {'square': 0, 'landscape': 0, 'portrait': 0},
            'color_modes': Counter(),
            'average_size': 0,
            'largest_image': None,
            'smallest_image': None
        }
        
        for image_path in image_paths:
            if not os.path.exists(image_path):
                continue
                
            try:
                # File size
                file_size = os.path.getsize(image_path)
                stats['total_size'] += file_size
                
                # Image properties
                with Image.open(image_path) as img:
                    width, height = img.size
                    stats['formats'][img.format] += 1
                    stats['color_modes'][img.mode] += 1
                    stats['dimensions'].append((width, height))
                    
                    # Size categories (by pixel count)
                    pixel_count = width * height
                    if pixel_count < 100000:  # < 0.1MP
                        stats['size_distribution']['small'] += 1
                    elif pixel_count < 1000000:  # < 1MP
                        stats['size_distribution']['medium'] += 1
                    elif pixel_count < 5000000:  # < 5MP
                        stats['size_distribution']['large'] += 1
                    else:
                        stats['size_distribution']['xlarge'] += 1
                        
                    # Aspect ratios
                    aspect_ratio = width / height
                    if 0.9 <= aspect_ratio <= 1.1:
                        stats['aspect_ratios']['square'] += 1
                    elif aspect_ratio > 1.1:
                        stats['aspect_ratios']['landscape'] += 1
                    else:
                        stats['aspect_ratios']['portrait'] += 1
                        
                    # Track largest/smallest
                    if stats['largest_image'] is None or pixel_count > stats['largest_image'][2]:
                        stats['largest_image'] = (width, height, pixel_count, image_path)
                    if stats['smallest_image'] is None or pixel_count < stats['smallest_image'][2]:
                        stats['smallest_image'] = (width, height, pixel_count, image_path)
                        
            except Exception:
                continue
                
        # Calculate averages
        if stats['total_images'] > 0:
            stats['average_size'] = int(stats['total_size'] / stats['total_images'])
            
        return stats


class AnalyticsDialog(ctk.CTkToplevel):
    """Dialog for displaying analytics and statistics"""
    
    def __init__(self, parent, analytics_manager: AnalyticsManager, image_paths: Optional[List[str]] = None):
        super().__init__(parent)
        
        self.title("Analytics & Statistics")
        self.geometry("900x700")
        self.transient(parent)
        
        self.analytics_manager = analytics_manager
        self.image_paths = image_paths or []
        
        self.create_widgets()
        self.load_data()
        
    def create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame, 
            text="📊 Analytics & Statistics", 
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(10, 20))
        
        # Tabview
        self.tabview = ctk.CTkTabview(main_frame)
        self.tabview.pack(fill="both", expand=True, pady=(0, 20))
        
        # Create tabs
        self.current_tab = self.tabview.add("Current Session")
        self.history_tab = self.tabview.add("History")
        self.collection_tab = self.tabview.add("Collection Analysis")
        
        # Close button
        close_btn = ctk.CTkButton(main_frame, text="Close", command=self.destroy)
        close_btn.pack(pady=10)
        
    def load_data(self):
        """Load and display analytics data"""
        self.load_current_session_data()
        self.load_history_data()
        if self.image_paths:
            self.load_collection_data()
            
    def load_current_session_data(self):
        """Load current session statistics"""
        stats = self.analytics_manager.get_current_session_stats()
        
        if stats:
            content = f"""
Current Crawling Session

Session ID: {stats['session_id']}
Start URL: {stats['start_url']}
Duration: {stats['duration']:.1f} seconds

Images Found: {stats['images_found']}
Images Downloaded: {stats['images_downloaded']}
Images Failed: {stats['images_failed']}
Success Rate: {stats['success_rate']:.1f}%

Total Size: {self.format_size(stats['total_size'])}
Domains Crawled: {stats['domains_crawled']}

File Types:
"""
            for file_type, count in stats['file_types'].items():
                content += f"  {file_type}: {count}\n"
        else:
            content = "No active crawling session"
            
        text_widget = ctk.CTkTextbox(self.current_tab)
        text_widget.pack(fill="both", expand=True, padx=10, pady=10)
        text_widget.insert("1.0", content)
        text_widget.configure(state="disabled")
        
    def load_history_data(self):
        """Load historical statistics"""
        stats = self.analytics_manager.get_historical_stats(30)
        sessions = self.analytics_manager.get_recent_sessions(10)
        
        content = f"""
Historical Statistics (Last 30 Days)

Total Sessions: {stats.get('total_sessions', 0)}
Total Images Found: {stats.get('total_found', 0)}
Total Images Downloaded: {stats.get('total_downloaded', 0)}
Total Images Failed: {stats.get('total_failed', 0)}
Total Size Downloaded: {self.format_size(stats.get('total_size', 0))}
Average Downloads per Session: {stats.get('avg_downloaded_per_session', 0):.1f}

File Type Distribution:
"""
        for file_type, count in stats.get('file_type_distribution', {}).items():
            content += f"  {file_type}: {count}\n"
            
        content += "\n\nRecent Sessions:\n"
        for session in sessions[:5]:
            start_time = session.get('start_time', '')
            if start_time:
                start_time = datetime.fromisoformat(start_time).strftime('%Y-%m-%d %H:%M')
            content += f"  {start_time} - {session.get('start_url', 'Unknown')} - {session.get('images_downloaded', 0)} images\n"
            
        text_widget = ctk.CTkTextbox(self.history_tab)
        text_widget.pack(fill="both", expand=True, padx=10, pady=10)
        text_widget.insert("1.0", content)
        text_widget.configure(state="disabled")
        
    def load_collection_data(self):
        """Load collection analysis data"""
        stats = self.analytics_manager.analyze_image_collection(self.image_paths)
        
        content = f"""
Image Collection Analysis

Total Images: {stats['total_images']}
Total Size: {self.format_size(stats['total_size'])}
Average Size: {self.format_size(stats['average_size'])}

Format Distribution:
"""
        for format_type, count in stats['formats'].items():
            percentage = (count / stats['total_images']) * 100
            content += f"  {format_type}: {count} ({percentage:.1f}%)\n"
            
        content += f"""
Size Distribution:
  Small (< 0.1MP): {stats['size_distribution']['small']}
  Medium (0.1-1MP): {stats['size_distribution']['medium']}
  Large (1-5MP): {stats['size_distribution']['large']}
  X-Large (> 5MP): {stats['size_distribution']['xlarge']}

Aspect Ratios:
  Square: {stats['aspect_ratios']['square']}
  Landscape: {stats['aspect_ratios']['landscape']}
  Portrait: {stats['aspect_ratios']['portrait']}

Color Modes:
"""
        for mode, count in stats['color_modes'].items():
            content += f"  {mode}: {count}\n"
            
        if stats['largest_image']:
            w, h, pixels, path = stats['largest_image']
            content += f"\nLargest Image: {w}×{h} ({pixels:,} pixels)\n  {os.path.basename(path)}\n"
            
        if stats['smallest_image']:
            w, h, pixels, path = stats['smallest_image']
            content += f"\nSmallest Image: {w}×{h} ({pixels:,} pixels)\n  {os.path.basename(path)}\n"
            
        text_widget = ctk.CTkTextbox(self.collection_tab)
        text_widget.pack(fill="both", expand=True, padx=10, pady=10)
        text_widget.insert("1.0", content)
        text_widget.configure(state="disabled")
        
    def format_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format"""
        size_float = float(size_bytes)
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_float < 1024.0:
                return f"{size_float:.1f} {unit}"
            size_float /= 1024.0
        return f"{size_float:.1f} TB"


def show_analytics_dialog(parent, analytics_manager: AnalyticsManager, image_paths: Optional[List[str]] = None):
    """Show analytics dialog"""
    dialog = AnalyticsDialog(parent, analytics_manager, image_paths)
    return dialog
