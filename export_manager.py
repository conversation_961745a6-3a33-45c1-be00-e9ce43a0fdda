"""
Export manager for the Image Crawler application.
Provides various export formats and batch operations.
"""

import os
import json
import csv
import zipfile
import shutil
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional
import threading
from PIL import Image
import customtkinter as ctk
from tkinter import filedialog, messagebox


class ExportManager:
    """Manages various export operations for crawled images"""
    
    def __init__(self, callback=None):
        self.callback = callback
        self.export_formats = {
            'json': self.export_to_json,
            'csv': self.export_to_csv,
            'html': self.export_to_html,
            'zip': self.export_to_zip,
            'folder': self.export_to_folder
        }
        
    def log(self, level: str, message: str):
        """Send log message via callback"""
        if self.callback:
            self.callback('log', {'level': level, 'message': message})
            
    def update_progress(self, progress: float, status: str = None):
        """Update progress via callback"""
        if self.callback:
            self.callback('progress_update', {'progress': progress, 'status': status})
            
    def export_images(self, images_data: List[Dict], export_format: str,
                     output_path: str, options: Optional[Dict] = None) -> bool:
        """
        Export images in specified format
        
        Args:
            images_data: List of image dictionaries with metadata
            export_format: Format to export ('json', 'csv', 'html', 'zip', 'folder')
            output_path: Output file/directory path
            options: Additional export options
            
        Returns:
            bool: Success status
        """
        try:
            if export_format not in self.export_formats:
                raise ValueError(f"Unsupported export format: {export_format}")
                
            self.log('INFO', f"Starting export to {export_format} format")
            
            # Call appropriate export function
            success = self.export_formats[export_format](images_data, output_path, options or {})
            
            if success:
                self.log('SUCCESS', f"Export completed successfully: {output_path}")
            else:
                self.log('ERROR', f"Export failed for format: {export_format}")
                
            return success
            
        except Exception as e:
            self.log('ERROR', f"Export error: {str(e)}")
            return False
            
    def export_to_json(self, images_data: List[Dict], output_path: str, options: Dict) -> bool:
        """Export image metadata to JSON format"""
        try:
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'total_images': len(images_data),
                    'export_format': 'json',
                    'version': '1.0'
                },
                'images': []
            }
            
            for i, img_data in enumerate(images_data):
                self.update_progress((i / len(images_data)) * 100, f"Processing image {i+1}/{len(images_data)}")
                
                # Extract image metadata
                img_info = {
                    'filename': os.path.basename(img_data.get('path', '')),
                    'original_url': img_data.get('url', ''),
                    'file_size': img_data.get('size', 0),
                    'download_time': img_data.get('download_time', ''),
                    'file_type': img_data.get('type', ''),
                    'local_path': img_data.get('path', '')
                }
                
                # Add image dimensions if available
                if os.path.exists(img_data.get('path', '')):
                    try:
                        with Image.open(img_data['path']) as img:
                            img_info['width'] = img.width
                            img_info['height'] = img.height
                            img_info['format'] = img.format
                    except Exception:
                        pass
                        
                export_data['images'].append(img_info)
                
            # Write JSON file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
                
            return True
            
        except Exception as e:
            self.log('ERROR', f"JSON export error: {str(e)}")
            return False
            
    def export_to_csv(self, images_data: List[Dict], output_path: str, options: Dict) -> bool:
        """Export image metadata to CSV format"""
        try:
            fieldnames = ['filename', 'original_url', 'file_size', 'download_time', 
                         'file_type', 'width', 'height', 'format', 'local_path']
                         
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for i, img_data in enumerate(images_data):
                    self.update_progress((i / len(images_data)) * 100, f"Processing image {i+1}/{len(images_data)}")
                    
                    row = {
                        'filename': os.path.basename(img_data.get('path', '')),
                        'original_url': img_data.get('url', ''),
                        'file_size': img_data.get('size', 0),
                        'download_time': img_data.get('download_time', ''),
                        'file_type': img_data.get('type', ''),
                        'local_path': img_data.get('path', ''),
                        'width': '',
                        'height': '',
                        'format': ''
                    }
                    
                    # Add image dimensions if available
                    if os.path.exists(img_data.get('path', '')):
                        try:
                            with Image.open(img_data['path']) as img:
                                row['width'] = img.width
                                row['height'] = img.height
                                row['format'] = img.format
                        except Exception:
                            pass
                            
                    writer.writerow(row)
                    
            return True
            
        except Exception as e:
            self.log('ERROR', f"CSV export error: {str(e)}")
            return False
            
    def export_to_html(self, images_data: List[Dict], output_path: str, options: Dict) -> bool:
        """Export images to HTML gallery format"""
        try:
            html_content = self._generate_html_gallery(images_data, options)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
            return True
            
        except Exception as e:
            self.log('ERROR', f"HTML export error: {str(e)}")
            return False
            
    def export_to_zip(self, images_data: List[Dict], output_path: str, options: Dict) -> bool:
        """Export images to ZIP archive"""
        try:
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add metadata file
                metadata = {
                    'export_info': {
                        'timestamp': datetime.now().isoformat(),
                        'total_images': len(images_data),
                        'export_format': 'zip'
                    },
                    'images': []
                }
                
                for i, img_data in enumerate(images_data):
                    self.update_progress((i / len(images_data)) * 100, f"Adding image {i+1}/{len(images_data)}")
                    
                    img_path = img_data.get('path', '')
                    if os.path.exists(img_path):
                        # Add image to zip
                        arcname = os.path.basename(img_path)
                        zipf.write(img_path, arcname)
                        
                        # Add to metadata
                        metadata['images'].append({
                            'filename': arcname,
                            'original_url': img_data.get('url', ''),
                            'file_size': img_data.get('size', 0)
                        })
                        
                # Add metadata file
                zipf.writestr('metadata.json', json.dumps(metadata, indent=2))
                
            return True
            
        except Exception as e:
            self.log('ERROR', f"ZIP export error: {str(e)}")
            return False
            
    def export_to_folder(self, images_data: List[Dict], output_path: str, options: Dict) -> bool:
        """Export images to organized folder structure"""
        try:
            # Create output directory
            os.makedirs(output_path, exist_ok=True)
            
            # Create subdirectories
            images_dir = os.path.join(output_path, 'images')
            os.makedirs(images_dir, exist_ok=True)
            
            copied_images = []
            
            for i, img_data in enumerate(images_data):
                self.update_progress((i / len(images_data)) * 100, f"Copying image {i+1}/{len(images_data)}")
                
                img_path = img_data.get('path', '')
                if os.path.exists(img_path):
                    filename = os.path.basename(img_path)
                    dest_path = os.path.join(images_dir, filename)
                    
                    # Handle duplicate filenames
                    counter = 1
                    base_name, ext = os.path.splitext(filename)
                    while os.path.exists(dest_path):
                        filename = f"{base_name}_{counter}{ext}"
                        dest_path = os.path.join(images_dir, filename)
                        counter += 1
                        
                    shutil.copy2(img_path, dest_path)
                    copied_images.append({
                        'filename': filename,
                        'original_url': img_data.get('url', ''),
                        'file_size': img_data.get('size', 0)
                    })
                    
            # Create metadata file
            metadata_path = os.path.join(output_path, 'metadata.json')
            metadata = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'total_images': len(copied_images),
                    'export_format': 'folder'
                },
                'images': copied_images
            }
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
                
            return True
            
        except Exception as e:
            self.log('ERROR', f"Folder export error: {str(e)}")
            return False
            
    def _generate_html_gallery(self, images_data: List[Dict], options: Dict) -> str:
        """Generate HTML gallery content"""
        title = options.get('title', 'Image Gallery')
        thumbnail_size = options.get('thumbnail_size', 200)
        
        html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .gallery {{ display: grid; grid-template-columns: repeat(auto-fill, minmax({thumbnail_size}px, 1fr)); gap: 20px; }}
        .image-item {{ background: white; border-radius: 8px; padding: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .image-item img {{ width: 100%; height: {thumbnail_size}px; object-fit: cover; border-radius: 4px; }}
        .image-info {{ margin-top: 10px; font-size: 12px; color: #666; }}
        .filename {{ font-weight: bold; margin-bottom: 5px; }}
        .metadata {{ display: none; }}
        .show-metadata {{ background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{title}</h1>
        <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>Total Images: {len(images_data)}</p>
    </div>
    <div class="gallery">
"""
        
        for img_data in images_data:
            img_path = img_data.get('path', '')
            if os.path.exists(img_path):
                filename = os.path.basename(img_path)
                html += f"""
        <div class="image-item">
            <img src="{img_path}" alt="{filename}" loading="lazy">
            <div class="image-info">
                <div class="filename">{filename}</div>
                <button class="show-metadata" onclick="toggleMetadata(this)">Show Details</button>
                <div class="metadata">
                    <p><strong>URL:</strong> {img_data.get('url', 'N/A')}</p>
                    <p><strong>Size:</strong> {img_data.get('size', 'N/A')}</p>
                    <p><strong>Type:</strong> {img_data.get('type', 'N/A')}</p>
                </div>
            </div>
        </div>
"""
        
        html += """
    </div>
    <script>
        function toggleMetadata(button) {
            const metadata = button.nextElementSibling;
            if (metadata.style.display === 'none' || metadata.style.display === '') {
                metadata.style.display = 'block';
                button.textContent = 'Hide Details';
            } else {
                metadata.style.display = 'none';
                button.textContent = 'Show Details';
            }
        }
    </script>
</body>
</html>
"""
        
        return html


class ExportDialog(ctk.CTkToplevel):
    """Dialog for configuring and executing exports"""
    
    def __init__(self, parent, images_data: List[Dict]):
        super().__init__(parent)
        
        self.title("Export Images")
        self.geometry("500x400")
        self.transient(parent)
        self.grab_set()
        
        self.images_data = images_data
        self.export_manager = ExportManager()
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame, 
            text="📤 Export Images", 
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(10, 20))
        
        # Export format
        format_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        format_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(format_frame, text="Export Format:").pack(anchor="w")
        
        self.format_var = ctk.StringVar(value="zip")
        formats = [
            ("ZIP Archive", "zip"),
            ("JSON Metadata", "json"),
            ("CSV Spreadsheet", "csv"),
            ("HTML Gallery", "html"),
            ("Organized Folder", "folder")
        ]
        
        for text, value in formats:
            radio = ctk.CTkRadioButton(format_frame, text=text, variable=self.format_var, value=value)
            radio.pack(anchor="w", pady=2)
            
        # Output path
        path_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        path_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(path_frame, text="Output Path:").pack(anchor="w")
        
        path_entry_frame = ctk.CTkFrame(path_frame, fg_color="transparent")
        path_entry_frame.pack(fill="x", pady=(5, 0))
        
        self.path_entry = ctk.CTkEntry(path_entry_frame)
        self.path_entry.pack(side="left", fill="x", expand=True)
        
        browse_btn = ctk.CTkButton(
            path_entry_frame,
            text="📁",
            width=40,
            command=self.browse_output_path
        )
        browse_btn.pack(side="right", padx=(5, 0))
        
        # Options
        options_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        options_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(options_frame, text="Options:").pack(anchor="w")
        
        self.include_metadata = ctk.CTkCheckBox(options_frame, text="Include metadata")
        self.include_metadata.pack(anchor="w", pady=2)
        self.include_metadata.select()
        
        # Buttons
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=(20, 0))
        
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="Cancel",
            command=self.destroy
        )
        cancel_btn.pack(side="right", padx=(10, 0))
        
        export_btn = ctk.CTkButton(
            buttons_frame,
            text="🚀 Export",
            command=self.start_export
        )
        export_btn.pack(side="right")
        
    def browse_output_path(self):
        """Browse for output path"""
        format_type = self.format_var.get()
        
        if format_type in ['zip']:
            path = filedialog.asksaveasfilename(
                title="Save Export As",
                defaultextension=f".{format_type}",
                filetypes=[(f"{format_type.upper()} files", f"*.{format_type}")]
            )
        elif format_type in ['json', 'csv', 'html']:
            path = filedialog.asksaveasfilename(
                title="Save Export As",
                defaultextension=f".{format_type}",
                filetypes=[(f"{format_type.upper()} files", f"*.{format_type}")]
            )
        else:  # folder
            path = filedialog.askdirectory(title="Select Export Directory")
            
        if path:
            self.path_entry.delete(0, ctk.END)
            self.path_entry.insert(0, path)
            
    def start_export(self):
        """Start the export process"""
        output_path = self.path_entry.get().strip()
        if not output_path:
            messagebox.showerror("Error", "Please select an output path", parent=self)
            return
            
        format_type = self.format_var.get()
        options = {
            'include_metadata': self.include_metadata.get(),
            'title': 'Image Crawler Gallery',
            'thumbnail_size': 200
        }
        
        # Start export in background thread
        def export_thread():
            success = self.export_manager.export_images(
                self.images_data, format_type, output_path, options
            )
            
            if success:
                messagebox.showinfo("Export Complete", f"Images exported successfully to:\n{output_path}", parent=self)
            else:
                messagebox.showerror("Export Failed", "Failed to export images. Check the logs for details.", parent=self)
                
        threading.Thread(target=export_thread, daemon=True).start()
        self.destroy()


def show_export_dialog(parent, images_data: List[Dict]):
    """Show export dialog"""
    dialog = ExportDialog(parent, images_data)
    return dialog
