"""
Enhanced dialog system for the Image Crawler application.
Provides better user feedback, error reporting, and interactive dialogs.
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, filedialog
import threading
import time
from datetime import datetime
from typing import Optional, Callable, Dict, Any
import os
import webbrowser


class ProgressDialog(ctk.CTkToplevel):
    """Enhanced progress dialog with detailed information"""
    
    def __init__(self, parent, title="Progress", cancelable=True):
        super().__init__(parent)
        
        self.title(title)
        self.geometry("500x300")
        self.transient(parent)
        self.grab_set()
        
        # Center on parent
        self.center_on_parent(parent)
        
        self.cancelable = cancelable
        self.cancelled = False
        self.cancel_callback = None
        
        self.create_widgets()
        
    def center_on_parent(self, parent):
        """Center dialog on parent window"""
        parent.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - 250
        y = parent.winfo_y() + (parent.winfo_height() // 2) - 150
        self.geometry(f"500x300+{x}+{y}")
        
    def create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        self.title_label = ctk.CTkLabel(
            main_frame, 
            text="Processing...", 
            font=ctk.CTkFont(size=18, weight="bold")
        )
        self.title_label.pack(pady=(10, 20))
        
        # Status
        self.status_label = ctk.CTkLabel(main_frame, text="Initializing...")
        self.status_label.pack(pady=(0, 10))
        
        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(main_frame, width=400)
        self.progress_bar.pack(pady=(0, 10))
        self.progress_bar.set(0)
        
        # Progress text
        self.progress_text = ctk.CTkLabel(main_frame, text="0%")
        self.progress_text.pack(pady=(0, 20))
        
        # Details frame
        details_frame = ctk.CTkFrame(main_frame)
        details_frame.pack(fill="both", expand=True, pady=(0, 20))
        
        # Details text
        self.details_text = ctk.CTkTextbox(details_frame, height=100)
        self.details_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Buttons frame
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x")
        
        if self.cancelable:
            self.cancel_button = ctk.CTkButton(
                buttons_frame,
                text="Cancel",
                command=self.cancel_operation,
                fg_color="red",
                hover_color="darkred"
            )
            self.cancel_button.pack(side="right", padx=(10, 0))
            
        self.minimize_button = ctk.CTkButton(
            buttons_frame,
            text="Minimize",
            command=self.iconify
        )
        self.minimize_button.pack(side="right")
        
    def update_progress(self, progress: float, status: str = None, details: str = None):
        """Update progress dialog"""
        self.progress_bar.set(progress / 100)
        self.progress_text.configure(text=f"{progress:.1f}%")
        
        if status:
            self.status_label.configure(text=status)
            
        if details:
            self.details_text.insert("end", f"[{datetime.now().strftime('%H:%M:%S')}] {details}\n")
            self.details_text.see("end")
            
    def set_cancel_callback(self, callback: Callable):
        """Set callback for cancel operation"""
        self.cancel_callback = callback
        
    def cancel_operation(self):
        """Cancel the operation"""
        self.cancelled = True
        if self.cancel_callback:
            self.cancel_callback()
        self.destroy()
        
    def close_dialog(self):
        """Close the dialog"""
        self.destroy()


class ErrorReportDialog(ctk.CTkToplevel):
    """Enhanced error reporting dialog"""
    
    def __init__(self, parent, error_title: str, error_message: str,
                 error_details: Optional[str] = None, suggestions: Optional[list] = None):
        super().__init__(parent)
        
        self.title("Error Report")
        self.geometry("600x500")
        self.transient(parent)
        self.grab_set()
        
        # Center on parent
        self.center_on_parent(parent)
        
        self.error_title = error_title
        self.error_message = error_message
        self.error_details = error_details or ""
        self.suggestions = suggestions or []
        
        self.create_widgets()
        
    def center_on_parent(self, parent):
        """Center dialog on parent window"""
        parent.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - 300
        y = parent.winfo_y() + (parent.winfo_height() // 2) - 250
        self.geometry(f"600x500+{x}+{y}")
        
    def create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Error icon and title
        header_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        header_frame.pack(fill="x", pady=(10, 20))
        
        error_icon = ctk.CTkLabel(header_frame, text="⚠️", font=ctk.CTkFont(size=24))
        error_icon.pack(side="left", padx=(0, 10))
        
        title_label = ctk.CTkLabel(
            header_frame, 
            text=self.error_title, 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(side="left")
        
        # Error message
        message_label = ctk.CTkLabel(main_frame, text=self.error_message, wraplength=500)
        message_label.pack(fill="x", pady=(0, 20))
        
        # Suggestions
        if self.suggestions:
            suggestions_label = ctk.CTkLabel(
                main_frame, 
                text="💡 Suggestions:", 
                font=ctk.CTkFont(weight="bold")
            )
            suggestions_label.pack(anchor="w", pady=(0, 10))
            
            for suggestion in self.suggestions:
                suggestion_label = ctk.CTkLabel(main_frame, text=f"• {suggestion}", wraplength=500)
                suggestion_label.pack(anchor="w", padx=(20, 0), pady=(0, 5))
        
        # Details section
        if self.error_details:
            details_label = ctk.CTkLabel(
                main_frame, 
                text="🔍 Technical Details:", 
                font=ctk.CTkFont(weight="bold")
            )
            details_label.pack(anchor="w", pady=(20, 10))
            
            details_text = ctk.CTkTextbox(main_frame, height=150)
            details_text.pack(fill="both", expand=True, pady=(0, 20))
            details_text.insert("1.0", self.error_details)
            details_text.configure(state="disabled")
        
        # Buttons
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x")
        
        copy_button = ctk.CTkButton(
            buttons_frame,
            text="📋 Copy Details",
            command=self.copy_details
        )
        copy_button.pack(side="left")
        
        help_button = ctk.CTkButton(
            buttons_frame,
            text="❓ Get Help",
            command=self.get_help
        )
        help_button.pack(side="left", padx=(10, 0))
        
        ok_button = ctk.CTkButton(
            buttons_frame,
            text="OK",
            command=self.destroy
        )
        ok_button.pack(side="right")
        
    def copy_details(self):
        """Copy error details to clipboard"""
        details = f"Error: {self.error_title}\n"
        details += f"Message: {self.error_message}\n"
        if self.error_details:
            details += f"Details: {self.error_details}\n"
        if self.suggestions:
            details += f"Suggestions: {', '.join(self.suggestions)}\n"
        details += f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        self.clipboard_clear()
        self.clipboard_append(details)
        
        # Show confirmation
        messagebox.showinfo("Copied", "Error details copied to clipboard", parent=self)
        
    def get_help(self):
        """Open help resources"""
        # This could open documentation, support site, etc.
        webbrowser.open("https://github.com/your-repo/image-crawler/issues")


class SettingsDialog(ctk.CTkToplevel):
    """Enhanced settings dialog with tabbed interface"""
    
    def __init__(self, parent, settings_manager):
        super().__init__(parent)
        
        self.title("Advanced Settings")
        self.geometry("800x600")
        self.transient(parent)
        self.grab_set()
        
        # Center on parent
        self.center_on_parent(parent)
        
        self.settings_manager = settings_manager
        self.settings = settings_manager.load_settings()
        self.temp_settings = self.settings.copy()
        
        self.create_widgets()
        
    def center_on_parent(self, parent):
        """Center dialog on parent window"""
        parent.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - 400
        y = parent.winfo_y() + (parent.winfo_height() // 2) - 300
        self.geometry(f"800x600+{x}+{y}")
        
    def create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame, 
            text="⚙️ Advanced Settings", 
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(10, 20))
        
        # Tabview
        self.tabview = ctk.CTkTabview(main_frame)
        self.tabview.pack(fill="both", expand=True, pady=(0, 20))
        
        # Create tabs
        self.create_general_tab()
        self.create_network_tab()
        self.create_cache_tab()
        self.create_advanced_tab()
        
        # Buttons
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x")
        
        reset_button = ctk.CTkButton(
            buttons_frame,
            text="🔄 Reset to Defaults",
            command=self.reset_to_defaults
        )
        reset_button.pack(side="left")
        
        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="Cancel",
            command=self.destroy
        )
        cancel_button.pack(side="right", padx=(10, 0))
        
        save_button = ctk.CTkButton(
            buttons_frame,
            text="💾 Save",
            command=self.save_settings
        )
        save_button.pack(side="right")
        
    def create_general_tab(self):
        """Create general settings tab"""
        tab = self.tabview.add("General")
        
        # Download directory
        dir_frame = ctk.CTkFrame(tab, fg_color="transparent")
        dir_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(dir_frame, text="Default Download Directory:").pack(anchor="w")
        
        dir_entry_frame = ctk.CTkFrame(dir_frame, fg_color="transparent")
        dir_entry_frame.pack(fill="x", pady=(5, 0))
        
        self.dir_entry = ctk.CTkEntry(dir_entry_frame)
        self.dir_entry.pack(side="left", fill="x", expand=True)
        self.dir_entry.insert(0, self.settings.get('download_dir', ''))
        
        browse_btn = ctk.CTkButton(
            dir_entry_frame,
            text="📁",
            width=40,
            command=self.browse_directory
        )
        browse_btn.pack(side="right", padx=(5, 0))
        
    def create_network_tab(self):
        """Create network settings tab"""
        tab = self.tabview.add("Network")
        
        # Timeout settings
        timeout_frame = ctk.CTkFrame(tab, fg_color="transparent")
        timeout_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(timeout_frame, text="Request Timeout (seconds):").pack(anchor="w")
        self.timeout_entry = ctk.CTkEntry(timeout_frame, width=100)
        self.timeout_entry.pack(anchor="w", pady=(5, 0))
        self.timeout_entry.insert(0, str(self.settings.get('request_timeout', 30)))
        
    def create_cache_tab(self):
        """Create cache settings tab"""
        tab = self.tabview.add("Cache")
        
        # Cache enabled
        self.cache_enabled = ctk.CTkCheckBox(tab, text="Enable Image Caching")
        self.cache_enabled.pack(anchor="w", pady=10)
        if self.settings.get('cache_enabled', True):
            self.cache_enabled.select()
            
    def create_advanced_tab(self):
        """Create advanced settings tab"""
        tab = self.tabview.add("Advanced")
        
        # Debug mode
        self.debug_mode = ctk.CTkCheckBox(tab, text="Enable Debug Mode")
        self.debug_mode.pack(anchor="w", pady=10)
        if self.settings.get('debug_mode', False):
            self.debug_mode.select()
            
    def browse_directory(self):
        """Browse for directory"""
        directory = filedialog.askdirectory(
            initialdir=self.dir_entry.get(),
            title="Select Default Download Directory"
        )
        if directory:
            self.dir_entry.delete(0, tk.END)
            self.dir_entry.insert(0, directory)
            
    def reset_to_defaults(self):
        """Reset settings to defaults"""
        if messagebox.askyesno("Reset Settings", "Reset all settings to defaults?", parent=self):
            self.settings_manager.reset_to_defaults()
            self.destroy()
            
    def save_settings(self):
        """Save settings"""
        # Update temp settings
        self.temp_settings['download_dir'] = self.dir_entry.get()
        self.temp_settings['request_timeout'] = int(self.timeout_entry.get())
        self.temp_settings['cache_enabled'] = self.cache_enabled.get()
        self.temp_settings['debug_mode'] = self.debug_mode.get()
        
        # Save to file
        for key, value in self.temp_settings.items():
            self.settings_manager.update_setting(key, value)
            
        messagebox.showinfo("Settings Saved", "Settings have been saved successfully", parent=self)
        self.destroy()


def show_error_dialog(parent, title: str, message: str, details: Optional[str] = None, suggestions: Optional[list] = None):
    """Show enhanced error dialog"""
    dialog = ErrorReportDialog(parent, title, message, details, suggestions)
    return dialog


def show_progress_dialog(parent, title: str = "Progress", cancelable: bool = True):
    """Show enhanced progress dialog"""
    dialog = ProgressDialog(parent, title, cancelable)
    return dialog


def show_settings_dialog(parent, settings_manager):
    """Show enhanced settings dialog"""
    dialog = SettingsDialog(parent, settings_manager)
    return dialog
