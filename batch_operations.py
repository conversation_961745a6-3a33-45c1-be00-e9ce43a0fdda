"""
Batch operations system for the Image Crawler application.
Provides bulk operations on multiple images like resize, convert, rename, etc.
"""

import os
import shutil
from datetime import datetime
from typing import List, Dict, Any, Callable, Optional
from pathlib import Path
import threading
from PIL import Image
import customtkinter as ctk
from tkinter import messagebox, filedialog


class BatchOperation:
    """Base class for batch operations"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        
    def execute(self, image_path: str, options: Optional[Dict] = None) -> bool:
        """Execute operation on a single image. Return True if successful."""
        raise NotImplementedError

    def validate_options(self, options: Optional[Dict] = None) -> tuple:
        """Validate operation options. Return (is_valid, error_message)."""
        return True, None


class ResizeOperation(BatchOperation):
    """Resize images to specified dimensions"""
    
    def __init__(self):
        super().__init__("Resize Images", "Resize images to specified dimensions")
        
    def execute(self, image_path: str, options: Optional[Dict] = None) -> bool:
        try:
            if not options:
                return False
            width = options.get('width', 800)
            height = options.get('height', 600)
            maintain_aspect = options.get('maintain_aspect', True)
            quality = options.get('quality', 95)

            with Image.open(image_path) as img:
                if maintain_aspect:
                    img.thumbnail((width, height), Image.Resampling.LANCZOS)
                else:
                    img = img.resize((width, height), Image.Resampling.LANCZOS)
                    
                # Save with original format or specified format
                output_format = options.get('format', img.format)
                if output_format == 'JPEG':
                    img = img.convert('RGB')
                    
                img.save(image_path, format=output_format, quality=quality, optimize=True)
                
            return True
        except Exception as e:
            print(f"Resize error for {image_path}: {e}")
            return False
            
    def validate_options(self, options: Dict) -> tuple:
        width = options.get('width')
        height = options.get('height')
        
        if not width or not height:
            return False, "Width and height are required"
        if width <= 0 or height <= 0:
            return False, "Width and height must be positive"
        if width > 10000 or height > 10000:
            return False, "Dimensions too large (max 10000px)"
            
        return True, None


class ConvertFormatOperation(BatchOperation):
    """Convert images to different format"""
    
    def __init__(self):
        super().__init__("Convert Format", "Convert images to different format")
        
    def execute(self, image_path: str, options: Optional[Dict] = None) -> bool:
        try:
            if not options:
                return False
            target_format = options.get('format', 'JPEG')
            quality = options.get('quality', 95)
            
            # Create new filename with new extension
            base_path = os.path.splitext(image_path)[0]
            ext_map = {'JPEG': '.jpg', 'PNG': '.png', 'WEBP': '.webp', 'BMP': '.bmp'}
            new_path = base_path + ext_map.get(target_format, '.jpg')
            
            with Image.open(image_path) as img:
                if target_format == 'JPEG' and img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                elif target_format == 'PNG' and img.mode == 'P':
                    img = img.convert('RGBA')
                    
                img.save(new_path, format=target_format, quality=quality, optimize=True)
                
            # Remove original if different path
            if new_path != image_path:
                os.remove(image_path)
                
            return True
        except Exception as e:
            print(f"Convert error for {image_path}: {e}")
            return False
            
    def validate_options(self, options: Dict) -> tuple:
        format_type = options.get('format')
        if format_type not in ['JPEG', 'PNG', 'WEBP', 'BMP']:
            return False, "Invalid format. Supported: JPEG, PNG, WEBP, BMP"
        return True, None


class RenameOperation(BatchOperation):
    """Rename images with pattern"""
    
    def __init__(self):
        super().__init__("Rename Images", "Rename images with custom pattern")
        
    def execute(self, image_path: str, options: Optional[Dict] = None) -> bool:
        try:
            if not options:
                return False
            pattern = options.get('pattern', 'image_{counter}')
            counter = options.get('counter', 1)
            
            directory = os.path.dirname(image_path)
            extension = os.path.splitext(image_path)[1]
            
            # Replace placeholders
            new_name = pattern.replace('{counter}', str(counter).zfill(3))
            new_name = new_name.replace('{date}', datetime.now().strftime('%Y%m%d'))
            new_name = new_name.replace('{time}', datetime.now().strftime('%H%M%S'))
            
            new_path = os.path.join(directory, new_name + extension)
            
            # Ensure unique filename
            counter_suffix = 1
            while os.path.exists(new_path):
                base_name = new_name + f"_{counter_suffix}"
                new_path = os.path.join(directory, base_name + extension)
                counter_suffix += 1
                
            os.rename(image_path, new_path)
            return True
        except Exception as e:
            print(f"Rename error for {image_path}: {e}")
            return False
            
    def validate_options(self, options: Dict) -> tuple:
        pattern = options.get('pattern', '')
        if not pattern:
            return False, "Pattern is required"
        return True, None


class CopyToFolderOperation(BatchOperation):
    """Copy images to specified folder"""
    
    def __init__(self):
        super().__init__("Copy to Folder", "Copy images to specified folder")
        
    def execute(self, image_path: str, options: Optional[Dict] = None) -> bool:
        try:
            if not options:
                return False
            target_folder = options.get('target_folder')
            if not target_folder:
                return False
                
            os.makedirs(target_folder, exist_ok=True)
            filename = os.path.basename(image_path)
            target_path = os.path.join(target_folder, filename)
            
            # Handle duplicate filenames
            counter = 1
            base_name, ext = os.path.splitext(filename)
            while os.path.exists(target_path):
                new_filename = f"{base_name}_{counter}{ext}"
                target_path = os.path.join(target_folder, new_filename)
                counter += 1
                
            shutil.copy2(image_path, target_path)
            return True
        except Exception as e:
            print(f"Copy error for {image_path}: {e}")
            return False
            
    def validate_options(self, options: Dict) -> tuple:
        target_folder = options.get('target_folder')
        if not target_folder:
            return False, "Target folder is required"
        return True, None


class BatchOperationManager:
    """Manages batch operations on multiple images"""
    
    def __init__(self, progress_callback: Optional[Callable] = None):
        self.progress_callback = progress_callback
        self.operations = {
            'resize': ResizeOperation(),
            'convert': ConvertFormatOperation(),
            'rename': RenameOperation(),
            'copy': CopyToFolderOperation()
        }
        
    def execute_operation(self, operation_name: str, image_paths: List[str], 
                         options: Dict) -> Dict[str, Any]:
        """Execute batch operation on multiple images"""
        if operation_name not in self.operations:
            return {'success': False, 'error': f'Unknown operation: {operation_name}'}
            
        operation = self.operations[operation_name]
        
        # Validate options
        is_valid, error_msg = operation.validate_options(options)
        if not is_valid:
            return {'success': False, 'error': error_msg}
            
        # Execute operation on each image
        results = {
            'success': True,
            'total_images': len(image_paths),
            'processed': 0,
            'failed': 0,
            'errors': []
        }
        
        for i, image_path in enumerate(image_paths):
            if self.progress_callback:
                self.progress_callback(i, len(image_paths), image_path)
                
            try:
                # Add counter for rename operation
                if operation_name == 'rename':
                    options['counter'] = i + 1
                    
                success = operation.execute(image_path, options)
                if success:
                    results['processed'] += 1
                else:
                    results['failed'] += 1
                    results['errors'].append(f"Failed to process: {image_path}")
            except Exception as e:
                results['failed'] += 1
                results['errors'].append(f"Error processing {image_path}: {str(e)}")
                
        return results


class BatchOperationDialog(ctk.CTkToplevel):
    """Dialog for configuring and executing batch operations"""
    
    def __init__(self, parent, image_paths: List[str]):
        super().__init__(parent)
        
        self.title("Batch Operations")
        self.geometry("600x500")
        self.transient(parent)
        self.grab_set()
        
        self.image_paths = image_paths
        self.batch_manager = BatchOperationManager()
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame, 
            text="🔧 Batch Operations", 
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(10, 20))
        
        # Info
        info_label = ctk.CTkLabel(
            main_frame, 
            text=f"Selected {len(self.image_paths)} images for batch processing"
        )
        info_label.pack(pady=(0, 20))
        
        # Operation selection
        op_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        op_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(op_frame, text="Operation:").pack(anchor="w")
        
        self.operation_var = ctk.StringVar(value="resize")
        operations = [
            ("Resize Images", "resize"),
            ("Convert Format", "convert"),
            ("Rename Images", "rename"),
            ("Copy to Folder", "copy")
        ]
        
        for text, value in operations:
            radio = ctk.CTkRadioButton(
                op_frame, 
                text=text, 
                variable=self.operation_var, 
                value=value,
                command=self.on_operation_changed
            )
            radio.pack(anchor="w", pady=2)
            
        # Options frame (will be populated based on operation)
        self.options_frame = ctk.CTkFrame(main_frame)
        self.options_frame.pack(fill="both", expand=True, pady=20)
        
        # Buttons
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x")
        
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="Cancel",
            command=self.destroy
        )
        cancel_btn.pack(side="right", padx=(10, 0))
        
        self.execute_btn = ctk.CTkButton(
            buttons_frame,
            text="🚀 Execute",
            command=self.execute_operation
        )
        self.execute_btn.pack(side="right")
        
        # Initialize options
        self.on_operation_changed()
        
    def on_operation_changed(self):
        """Handle operation selection change"""
        # Clear options frame
        for widget in self.options_frame.winfo_children():
            widget.destroy()
            
        operation = self.operation_var.get()
        
        if operation == "resize":
            self.create_resize_options()
        elif operation == "convert":
            self.create_convert_options()
        elif operation == "rename":
            self.create_rename_options()
        elif operation == "copy":
            self.create_copy_options()
            
    def create_resize_options(self):
        """Create resize operation options"""
        ctk.CTkLabel(self.options_frame, text="Resize Options:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        # Dimensions
        dims_frame = ctk.CTkFrame(self.options_frame, fg_color="transparent")
        dims_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(dims_frame, text="Width:").pack(side="left")
        self.width_entry = ctk.CTkEntry(dims_frame, width=80, placeholder_text="800")
        self.width_entry.pack(side="left", padx=(10, 20))
        
        ctk.CTkLabel(dims_frame, text="Height:").pack(side="left")
        self.height_entry = ctk.CTkEntry(dims_frame, width=80, placeholder_text="600")
        self.height_entry.pack(side="left", padx=10)
        
        # Options
        self.maintain_aspect = ctk.CTkCheckBox(self.options_frame, text="Maintain aspect ratio")
        self.maintain_aspect.pack(anchor="w", padx=10, pady=5)
        self.maintain_aspect.select()
        
        # Quality
        quality_frame = ctk.CTkFrame(self.options_frame, fg_color="transparent")
        quality_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(quality_frame, text="Quality (1-100):").pack(side="left")
        self.quality_entry = ctk.CTkEntry(quality_frame, width=60, placeholder_text="95")
        self.quality_entry.pack(side="left", padx=10)
        
    def create_convert_options(self):
        """Create convert operation options"""
        ctk.CTkLabel(self.options_frame, text="Convert Options:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        # Format selection
        format_frame = ctk.CTkFrame(self.options_frame, fg_color="transparent")
        format_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(format_frame, text="Target Format:").pack(side="left")
        
        self.format_var = ctk.StringVar(value="JPEG")
        format_menu = ctk.CTkOptionMenu(
            format_frame,
            values=["JPEG", "PNG", "WEBP", "BMP"],
            variable=self.format_var
        )
        format_menu.pack(side="left", padx=10)
        
    def create_rename_options(self):
        """Create rename operation options"""
        ctk.CTkLabel(self.options_frame, text="Rename Options:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        # Pattern
        pattern_frame = ctk.CTkFrame(self.options_frame, fg_color="transparent")
        pattern_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(pattern_frame, text="Pattern:").pack(anchor="w")
        self.pattern_entry = ctk.CTkEntry(pattern_frame, placeholder_text="image_{counter}")
        self.pattern_entry.pack(fill="x", pady=5)
        
        # Help text
        help_text = "Available placeholders:\n{counter} - Sequential number\n{date} - Current date (YYYYMMDD)\n{time} - Current time (HHMMSS)"
        ctk.CTkLabel(self.options_frame, text=help_text, font=ctk.CTkFont(size=10)).pack(anchor="w", padx=10, pady=5)
        
    def create_copy_options(self):
        """Create copy operation options"""
        ctk.CTkLabel(self.options_frame, text="Copy Options:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        # Target folder
        folder_frame = ctk.CTkFrame(self.options_frame, fg_color="transparent")
        folder_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(folder_frame, text="Target Folder:").pack(anchor="w")
        
        folder_entry_frame = ctk.CTkFrame(folder_frame, fg_color="transparent")
        folder_entry_frame.pack(fill="x", pady=5)
        
        self.folder_entry = ctk.CTkEntry(folder_entry_frame)
        self.folder_entry.pack(side="left", fill="x", expand=True)
        
        browse_btn = ctk.CTkButton(
            folder_entry_frame,
            text="📁",
            width=40,
            command=self.browse_folder
        )
        browse_btn.pack(side="right", padx=(5, 0))
        
    def browse_folder(self):
        """Browse for target folder"""
        folder = filedialog.askdirectory(title="Select Target Folder")
        if folder:
            self.folder_entry.delete(0, ctk.END)
            self.folder_entry.insert(0, folder)
            
    def execute_operation(self):
        """Execute the selected operation"""
        operation = self.operation_var.get()
        options = {}
        
        try:
            # Collect options based on operation
            if operation == "resize":
                options = {
                    'width': int(self.width_entry.get() or 800),
                    'height': int(self.height_entry.get() or 600),
                    'maintain_aspect': self.maintain_aspect.get(),
                    'quality': int(self.quality_entry.get() or 95)
                }
            elif operation == "convert":
                options = {
                    'format': self.format_var.get(),
                    'quality': 95
                }
            elif operation == "rename":
                options = {
                    'pattern': self.pattern_entry.get() or 'image_{counter}'
                }
            elif operation == "copy":
                options = {
                    'target_folder': self.folder_entry.get()
                }
                
            # Execute operation in background thread
            def execute_thread():
                results = self.batch_manager.execute_operation(operation, self.image_paths, options)
                
                # Show results
                if results['success']:
                    message = f"Batch operation completed!\n\nProcessed: {results['processed']}\nFailed: {results['failed']}"
                    if results['errors']:
                        message += f"\n\nErrors:\n" + "\n".join(results['errors'][:5])
                        if len(results['errors']) > 5:
                            message += f"\n... and {len(results['errors']) - 5} more errors"
                    messagebox.showinfo("Batch Operation Complete", message, parent=self)
                else:
                    messagebox.showerror("Batch Operation Failed", results.get('error', 'Unknown error'), parent=self)
                    
            threading.Thread(target=execute_thread, daemon=True).start()
            self.destroy()
            
        except ValueError as e:
            messagebox.showerror("Invalid Input", f"Please check your input values: {str(e)}", parent=self)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to execute operation: {str(e)}", parent=self)


def show_batch_operations_dialog(parent, image_paths: List[str]):
    """Show batch operations dialog"""
    if not image_paths:
        messagebox.showinfo("No Images", "No images selected for batch operations.")
        return
        
    dialog = BatchOperationDialog(parent, image_paths)
    return dialog
