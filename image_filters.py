"""
Advanced image filtering and search system for the Image Crawler application.
Provides comprehensive filtering options based on various image properties.
"""

import os
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any, Callable, Optional
from PIL import Image
import customtkinter as ctk
from tkinter import messagebox


class ImageFilter:
    """Base class for image filters"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        
    def apply(self, image_path: str, metadata: Optional[Dict] = None) -> bool:
        """Apply filter to image. Return True if image passes filter."""
        raise NotImplementedError
        

class SizeFilter(ImageFilter):
    """Filter images by dimensions"""
    
    def __init__(self, min_width: Optional[int] = None, max_width: Optional[int] = None,
                 min_height: Optional[int] = None, max_height: Optional[int] = None):
        super().__init__("Size Filter", "Filter by image dimensions")
        self.min_width = min_width
        self.max_width = max_width
        self.min_height = min_height
        self.max_height = max_height
        
    def apply(self, image_path: str, metadata: Optional[Dict] = None) -> bool:
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                
                if self.min_width and width < self.min_width:
                    return False
                if self.max_width and width > self.max_width:
                    return False
                if self.min_height and height < self.min_height:
                    return False
                if self.max_height and height > self.max_height:
                    return False
                    
                return True
        except Exception:
            return False


class FileSizeFilter(ImageFilter):
    """Filter images by file size"""
    
    def __init__(self, min_size: Optional[int] = None, max_size: Optional[int] = None):
        super().__init__("File Size Filter", "Filter by file size")
        self.min_size = min_size  # in bytes
        self.max_size = max_size  # in bytes
        
    def apply(self, image_path: str, metadata: Optional[Dict] = None) -> bool:
        try:
            file_size = os.path.getsize(image_path)
            
            if self.min_size and file_size < self.min_size:
                return False
            if self.max_size and file_size > self.max_size:
                return False
                
            return True
        except Exception:
            return False


class FormatFilter(ImageFilter):
    """Filter images by format"""
    
    def __init__(self, allowed_formats: List[str]):
        super().__init__("Format Filter", "Filter by image format")
        self.allowed_formats = [fmt.upper() for fmt in allowed_formats]
        
    def apply(self, image_path: str, metadata: Optional[Dict] = None) -> bool:
        try:
            with Image.open(image_path) as img:
                return img.format in self.allowed_formats
        except Exception:
            return False


class DateFilter(ImageFilter):
    """Filter images by creation/modification date"""
    
    def __init__(self, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None):
        super().__init__("Date Filter", "Filter by creation date")
        self.start_date = start_date
        self.end_date = end_date
        
    def apply(self, image_path: str, metadata: Optional[Dict] = None) -> bool:
        try:
            mod_time = datetime.fromtimestamp(os.path.getmtime(image_path))
            
            if self.start_date and mod_time < self.start_date:
                return False
            if self.end_date and mod_time > self.end_date:
                return False
                
            return True
        except Exception:
            return False


class NameFilter(ImageFilter):
    """Filter images by filename pattern"""
    
    def __init__(self, pattern: str, case_sensitive: bool = False):
        super().__init__("Name Filter", "Filter by filename pattern")
        self.pattern = pattern
        self.case_sensitive = case_sensitive
        
    def apply(self, image_path: str, metadata: Optional[Dict] = None) -> bool:
        try:
            filename = os.path.basename(image_path)
            if not self.case_sensitive:
                filename = filename.lower()
                pattern = self.pattern.lower()
            else:
                pattern = self.pattern
                
            # Support both regex and simple wildcard patterns
            if '*' in pattern or '?' in pattern:
                # Convert wildcard to regex
                regex_pattern = pattern.replace('*', '.*').replace('?', '.')
                return bool(re.search(regex_pattern, filename))
            else:
                # Simple substring search
                return pattern in filename
        except Exception:
            return False


class ColorModeFilter(ImageFilter):
    """Filter images by color mode (RGB, RGBA, L, etc.)"""
    
    def __init__(self, allowed_modes: List[str]):
        super().__init__("Color Mode Filter", "Filter by color mode")
        self.allowed_modes = allowed_modes
        
    def apply(self, image_path: str, metadata: Optional[Dict] = None) -> bool:
        try:
            with Image.open(image_path) as img:
                return img.mode in self.allowed_modes
        except Exception:
            return False


class ImageFilterManager:
    """Manages multiple image filters and applies them to image collections"""
    
    def __init__(self):
        self.filters: List[ImageFilter] = []
        self.filter_mode = "AND"  # "AND" or "OR"
        
    def add_filter(self, filter_obj: ImageFilter):
        """Add a filter to the manager"""
        self.filters.append(filter_obj)
        
    def remove_filter(self, filter_obj: ImageFilter):
        """Remove a filter from the manager"""
        if filter_obj in self.filters:
            self.filters.remove(filter_obj)
            
    def clear_filters(self):
        """Remove all filters"""
        self.filters.clear()
        
    def set_filter_mode(self, mode: str):
        """Set filter combination mode ('AND' or 'OR')"""
        if mode in ["AND", "OR"]:
            self.filter_mode = mode
            
    def apply_filters(self, image_paths: List[str],
                     progress_callback: Optional[Callable] = None) -> List[str]:
        """Apply all filters to a list of image paths"""
        if not self.filters:
            return image_paths
            
        filtered_paths = []
        total_images = len(image_paths)
        
        for i, image_path in enumerate(image_paths):
            if progress_callback:
                progress_callback(i, total_images, image_path)
                
            if self.filter_mode == "AND":
                # All filters must pass
                passes = all(f.apply(image_path) for f in self.filters)
            else:  # OR mode
                # At least one filter must pass
                passes = any(f.apply(image_path) for f in self.filters)
                
            if passes:
                filtered_paths.append(image_path)
                
        return filtered_paths
        
    def get_filter_summary(self) -> str:
        """Get a summary of active filters"""
        if not self.filters:
            return "No filters active"
            
        summary = f"Active filters ({self.filter_mode} mode):\n"
        for i, filter_obj in enumerate(self.filters, 1):
            summary += f"{i}. {filter_obj.name}: {filter_obj.description}\n"
            
        return summary.strip()


class FilterDialog(ctk.CTkToplevel):
    """Dialog for configuring image filters"""
    
    def __init__(self, parent, filter_manager: ImageFilterManager, apply_callback: Callable):
        super().__init__(parent)
        
        self.title("Image Filters")
        self.geometry("600x700")
        self.transient(parent)
        self.grab_set()
        
        self.filter_manager = filter_manager
        self.apply_callback = apply_callback
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame, 
            text="🔍 Image Filters", 
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(10, 20))
        
        # Filter mode
        mode_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        mode_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(mode_frame, text="Filter Mode:").pack(side="left")
        
        self.mode_var = ctk.StringVar(value=self.filter_manager.filter_mode)
        mode_and = ctk.CTkRadioButton(mode_frame, text="AND (all must match)", 
                                     variable=self.mode_var, value="AND")
        mode_and.pack(side="left", padx=(20, 10))
        
        mode_or = ctk.CTkRadioButton(mode_frame, text="OR (any must match)", 
                                    variable=self.mode_var, value="OR")
        mode_or.pack(side="left")
        
        # Tabview for different filter types
        self.tabview = ctk.CTkTabview(main_frame)
        self.tabview.pack(fill="both", expand=True, pady=20)
        
        # Create filter tabs
        self.create_size_tab()
        self.create_format_tab()
        self.create_name_tab()
        self.create_date_tab()
        
        # Active filters display
        active_frame = ctk.CTkFrame(main_frame)
        active_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(active_frame, text="Active Filters:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        self.active_filters_text = ctk.CTkTextbox(active_frame, height=80)
        self.active_filters_text.pack(fill="x", padx=10, pady=(0, 10))
        
        self.update_active_filters_display()
        
        # Buttons
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x")
        
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ Clear All",
            command=self.clear_all_filters
        )
        clear_btn.pack(side="left")
        
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="Cancel",
            command=self.destroy
        )
        cancel_btn.pack(side="right", padx=(10, 0))
        
        apply_btn = ctk.CTkButton(
            buttons_frame,
            text="✅ Apply Filters",
            command=self.apply_filters
        )
        apply_btn.pack(side="right")
        
    def create_size_tab(self):
        """Create size filter tab"""
        tab = self.tabview.add("Size")
        
        # Dimensions
        dims_frame = ctk.CTkFrame(tab, fg_color="transparent")
        dims_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(dims_frame, text="Image Dimensions:").pack(anchor="w")
        
        # Width
        width_frame = ctk.CTkFrame(dims_frame, fg_color="transparent")
        width_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(width_frame, text="Width:").pack(side="left")
        self.min_width_entry = ctk.CTkEntry(width_frame, placeholder_text="Min", width=80)
        self.min_width_entry.pack(side="left", padx=(10, 5))
        ctk.CTkLabel(width_frame, text="to").pack(side="left", padx=5)
        self.max_width_entry = ctk.CTkEntry(width_frame, placeholder_text="Max", width=80)
        self.max_width_entry.pack(side="left", padx=5)
        
        # Height
        height_frame = ctk.CTkFrame(dims_frame, fg_color="transparent")
        height_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(height_frame, text="Height:").pack(side="left")
        self.min_height_entry = ctk.CTkEntry(height_frame, placeholder_text="Min", width=80)
        self.min_height_entry.pack(side="left", padx=(10, 5))
        ctk.CTkLabel(height_frame, text="to").pack(side="left", padx=5)
        self.max_height_entry = ctk.CTkEntry(height_frame, placeholder_text="Max", width=80)
        self.max_height_entry.pack(side="left", padx=5)
        
        add_size_btn = ctk.CTkButton(dims_frame, text="Add Size Filter", command=self.add_size_filter)
        add_size_btn.pack(pady=10)
        
    def create_format_tab(self):
        """Create format filter tab"""
        tab = self.tabview.add("Format")
        
        format_frame = ctk.CTkFrame(tab, fg_color="transparent")
        format_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(format_frame, text="Allowed Formats:").pack(anchor="w")
        
        # Format checkboxes
        self.format_vars = {}
        formats = ["JPEG", "PNG", "GIF", "WEBP", "BMP", "TIFF"]
        
        for fmt in formats:
            var = ctk.BooleanVar()
            checkbox = ctk.CTkCheckBox(format_frame, text=fmt, variable=var)
            checkbox.pack(anchor="w", pady=2)
            self.format_vars[fmt] = var
            
        add_format_btn = ctk.CTkButton(format_frame, text="Add Format Filter", command=self.add_format_filter)
        add_format_btn.pack(pady=10)
        
    def create_name_tab(self):
        """Create name filter tab"""
        tab = self.tabview.add("Name")
        
        name_frame = ctk.CTkFrame(tab, fg_color="transparent")
        name_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(name_frame, text="Filename Pattern:").pack(anchor="w")
        
        self.name_pattern_entry = ctk.CTkEntry(name_frame, placeholder_text="e.g., *.jpg or photo*")
        self.name_pattern_entry.pack(fill="x", pady=5)
        
        self.case_sensitive_var = ctk.BooleanVar()
        case_checkbox = ctk.CTkCheckBox(name_frame, text="Case sensitive", variable=self.case_sensitive_var)
        case_checkbox.pack(anchor="w", pady=5)
        
        add_name_btn = ctk.CTkButton(name_frame, text="Add Name Filter", command=self.add_name_filter)
        add_name_btn.pack(pady=10)
        
    def create_date_tab(self):
        """Create date filter tab"""
        tab = self.tabview.add("Date")
        
        date_frame = ctk.CTkFrame(tab, fg_color="transparent")
        date_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(date_frame, text="Date Range (modification date):").pack(anchor="w")
        
        # Quick date options
        quick_frame = ctk.CTkFrame(date_frame, fg_color="transparent")
        quick_frame.pack(fill="x", pady=5)
        
        ctk.CTkLabel(quick_frame, text="Quick options:").pack(anchor="w")
        
        quick_btns = [
            ("Last 24 hours", lambda: self.set_date_range(hours=24)),
            ("Last week", lambda: self.set_date_range(days=7)),
            ("Last month", lambda: self.set_date_range(days=30))
        ]
        
        for text, command in quick_btns:
            btn = ctk.CTkButton(quick_frame, text=text, command=command, width=100)
            btn.pack(side="left", padx=5)
            
        add_date_btn = ctk.CTkButton(date_frame, text="Add Date Filter", command=self.add_date_filter)
        add_date_btn.pack(pady=10)
        
    def set_date_range(self, days=0, hours=0):
        """Set date range for quick options"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days, hours=hours)
        
        # Add date filter
        date_filter = DateFilter(start_date, end_date)
        self.filter_manager.add_filter(date_filter)
        self.update_active_filters_display()
        
    def add_size_filter(self):
        """Add size filter"""
        try:
            min_width = int(self.min_width_entry.get()) if self.min_width_entry.get() else None
            max_width = int(self.max_width_entry.get()) if self.max_width_entry.get() else None
            min_height = int(self.min_height_entry.get()) if self.min_height_entry.get() else None
            max_height = int(self.max_height_entry.get()) if self.max_height_entry.get() else None
            
            if any([min_width, max_width, min_height, max_height]):
                # Only pass non-None values to avoid type issues
                size_filter = SizeFilter(
                    min_width=min_width,
                    max_width=max_width,
                    min_height=min_height,
                    max_height=max_height
                )
                self.filter_manager.add_filter(size_filter)
                self.update_active_filters_display()
            else:
                messagebox.showwarning("Invalid Input", "Please enter at least one dimension value.")
        except ValueError:
            messagebox.showerror("Invalid Input", "Please enter valid numbers for dimensions.")
            
    def add_format_filter(self):
        """Add format filter"""
        selected_formats = [fmt for fmt, var in self.format_vars.items() if var.get()]
        
        if selected_formats:
            format_filter = FormatFilter(selected_formats)
            self.filter_manager.add_filter(format_filter)
            self.update_active_filters_display()
        else:
            messagebox.showwarning("No Selection", "Please select at least one format.")
            
    def add_name_filter(self):
        """Add name filter"""
        pattern = self.name_pattern_entry.get().strip()
        
        if pattern:
            name_filter = NameFilter(pattern, self.case_sensitive_var.get())
            self.filter_manager.add_filter(name_filter)
            self.update_active_filters_display()
        else:
            messagebox.showwarning("Empty Pattern", "Please enter a filename pattern.")
            
    def add_date_filter(self):
        """Add date filter (using quick options for now)"""
        messagebox.showinfo("Date Filter", "Use the quick options above to add date filters.")
        
    def update_active_filters_display(self):
        """Update the active filters display"""
        self.active_filters_text.delete("1.0", "end")
        summary = self.filter_manager.get_filter_summary()
        self.active_filters_text.insert("1.0", summary)
        
    def clear_all_filters(self):
        """Clear all filters"""
        self.filter_manager.clear_filters()
        self.update_active_filters_display()
        
    def apply_filters(self):
        """Apply filters and close dialog"""
        self.filter_manager.set_filter_mode(self.mode_var.get())
        self.apply_callback()
        self.destroy()


def show_filter_dialog(parent, filter_manager: ImageFilterManager, apply_callback: Callable):
    """Show image filter dialog"""
    dialog = FilterDialog(parent, filter_manager, apply_callback)
    return dialog
