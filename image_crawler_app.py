import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import os
import sys
import json
import time
from datetime import datetime
from PIL import Image, ImageTk
import requests
from urllib.parse import urljoin, urlparse

from crawler_engine import ImageCrawler
from image_viewer import ImageViewer
from settings_manager import SettingsManager
from input_validator import InputValidator
from lazy_gallery import LazyGallery
from image_cache import get_image_cache, configure_cache
from customtkinter import CTkImage
import tkinter.messagebox as messagebox

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class ImageCrawlerApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        # Stat card value label references
        self.stat_value_labels = {}
        # Stat item value widget references
        self.stat_item_value_widgets = {}
        
        self.title("Advanced Image Crawler")
        self.geometry("1400x900")
        self.minsize(1200, 700)
        
        # Initialize variables
        self.crawler = None
        self.crawler_thread = None
        self.download_queue = queue.Queue()
        self.is_crawling = False
        self.is_paused = False
        self.downloaded_images = []
        self.total_images_found = 0
        self.images_downloaded = 0
        self.images_failed = 0
        
        # Load settings
        self.settings_manager = SettingsManager(error_callback=self.settings_error_callback)
        self.settings = self.settings_manager.load_settings()
        
        # Configure grid
        self.grid_columnconfigure(0, weight=0)
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        # Create UI
        self.create_sidebar()
        self.create_main_content()
        self.create_status_bar()
        
        # Bind events
        self.bind_events()

        # Setup input validation
        self.setup_input_validation()
        
        # Start optimized queue processor
        self.queue_processing_active = True
        self.after(50, self.process_queue)  # Reduced from 100ms to 50ms

        # Start gallery statistics updater
        self.after(1000, self.update_gallery_statistics)  # Update every second
        
        # Set global exception handler for threads (Python 3.8+)
        if hasattr(threading, 'excepthook'):
            threading.excepthook = self.thread_exception_handler
        
    def create_sidebar(self):
        """Create the left sidebar with controls"""
        self.sidebar = ctk.CTkFrame(self, width=350, corner_radius=0)
        self.sidebar.grid(row=0, column=0, sticky="nsew")
        self.sidebar.grid_rowconfigure(10, weight=1)
        
        # Logo/Title
        self.logo_label = ctk.CTkLabel(
            self.sidebar, 
            text="🖼️ Image Crawler", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 30), columnspan=2)
        
        # URL Input Section
        self.url_label = ctk.CTkLabel(self.sidebar, text="Target URL:", font=ctk.CTkFont(size=14))
        self.url_label.grid(row=1, column=0, padx=20, pady=(10, 5), sticky="w")
        
        self.url_entry = ctk.CTkEntry(
            self.sidebar, 
            placeholder_text="https://example.com",
            height=35
        )
        self.url_entry.grid(row=2, column=0, padx=20, pady=(0, 10), sticky="ew", columnspan=2)
        
        # Download Directory
        self.dir_label = ctk.CTkLabel(self.sidebar, text="Download Directory:", font=ctk.CTkFont(size=14))
        self.dir_label.grid(row=3, column=0, padx=20, pady=(10, 5), sticky="w")
        
        self.dir_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        self.dir_frame.grid(row=4, column=0, padx=20, pady=(0, 10), sticky="ew", columnspan=2)
        
        self.dir_entry = ctk.CTkEntry(self.dir_frame, height=35)
        self.dir_entry.pack(side="left", fill="x", expand=True)
        self.dir_entry.insert(0, self.settings.get('download_dir', os.path.join(os.path.expanduser('~'), 'Downloads', 'ImageCrawler')))
        
        self.browse_btn = ctk.CTkButton(
            self.dir_frame, 
            text="📁", 
            width=35, 
            height=35,
            command=self.browse_directory
        )
        self.browse_btn.pack(side="right", padx=(5, 0))
        
        # Crawler Settings
        self.settings_label = ctk.CTkLabel(
            self.sidebar, 
            text="⚙️ Crawler Settings", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.settings_label.grid(row=5, column=0, padx=20, pady=(20, 10), sticky="w", columnspan=2)
        
        # Max Depth
        self.depth_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        self.depth_frame.grid(row=6, column=0, padx=20, pady=5, sticky="ew", columnspan=2)
        
        self.depth_label = ctk.CTkLabel(self.depth_frame, text="Max Depth:")
        self.depth_label.pack(side="left")
        
        self.depth_value_label = ctk.CTkLabel(self.depth_frame, text="2")
        self.depth_value_label.pack(side="right", padx=(10, 0))
        
        self.depth_slider = ctk.CTkSlider(
            self.depth_frame, 
            from_=1, 
            to=5,
            number_of_steps=4,
            command=lambda v: self.depth_value_label.configure(text=str(int(v)))
        )
        self.depth_slider.set(2)
        self.depth_slider.pack(side="right", fill="x", expand=True, padx=(10, 5))
        
        # Max Images
        self.max_images_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        self.max_images_frame.grid(row=7, column=0, padx=20, pady=5, sticky="ew", columnspan=2)
        
        self.max_images_label = ctk.CTkLabel(self.max_images_frame, text="Max Images:")
        self.max_images_label.pack(side="left")
        
        self.max_images_entry = ctk.CTkEntry(self.max_images_frame, width=80, height=30)
        self.max_images_entry.insert(0, "100")
        self.max_images_entry.pack(side="right")
        
        # Concurrent Downloads
        self.concurrent_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        self.concurrent_frame.grid(row=8, column=0, padx=20, pady=5, sticky="ew", columnspan=2)
        
        self.concurrent_label = ctk.CTkLabel(self.concurrent_frame, text="Concurrent Downloads:")
        self.concurrent_label.pack(side="left")
        
        self.concurrent_value_label = ctk.CTkLabel(self.concurrent_frame, text="5")
        self.concurrent_value_label.pack(side="right", padx=(10, 0))
        
        self.concurrent_slider = ctk.CTkSlider(
            self.concurrent_frame, 
            from_=1, 
            to=10,
            number_of_steps=9,
            command=lambda v: self.concurrent_value_label.configure(text=str(int(v)))
        )
        self.concurrent_slider.set(5)
        self.concurrent_slider.pack(side="right", fill="x", expand=True, padx=(10, 5))
        
        # Image Filters
        self.filters_label = ctk.CTkLabel(
            self.sidebar, 
            text="🎯 Image Filters", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.filters_label.grid(row=9, column=0, padx=20, pady=(20, 10), sticky="w", columnspan=2)
        
        # Min Size Filter
        self.min_size_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        self.min_size_frame.grid(row=10, column=0, padx=20, pady=5, sticky="ew", columnspan=2)
        
        self.min_size_check = ctk.CTkCheckBox(
            self.min_size_frame, 
            text="Min Size (px):",
            width=120
        )
        self.min_size_check.pack(side="left")
        
        self.min_width_entry = ctk.CTkEntry(self.min_size_frame, width=60, height=30, placeholder_text="Width")
        self.min_width_entry.pack(side="left", padx=(5, 2))
        self.min_width_entry.insert(0, "100")
        
        self.min_height_entry = ctk.CTkEntry(self.min_size_frame, width=60, height=30, placeholder_text="Height")
        self.min_height_entry.pack(side="left", padx=(2, 0))
        self.min_height_entry.insert(0, "100")
        
        # File Types
        self.types_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        self.types_frame.grid(row=11, column=0, padx=20, pady=5, sticky="ew", columnspan=2)
        
        self.types_label = ctk.CTkLabel(self.types_frame, text="File Types:")
        self.types_label.pack(side="left")
        
        self.types_entry = ctk.CTkEntry(self.types_frame, placeholder_text="jpg,png,gif,webp")
        self.types_entry.pack(side="left", fill="x", expand=True, padx=(10, 0))
        self.types_entry.insert(0, "jpg,jpeg,png,gif,webp,bmp")
        
        # Control Buttons
        self.controls_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        self.controls_frame.grid(row=12, column=0, padx=20, pady=(30, 10), sticky="ew", columnspan=2)
        
        self.start_btn = ctk.CTkButton(
            self.controls_frame,
            text="▶️ Start Crawling",
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.start_crawling
        )
        self.start_btn.pack(side="left", fill="x", expand=True, padx=(0, 5))
        
        self.pause_btn = ctk.CTkButton(
            self.controls_frame,
            text="⏸️ Pause",
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.pause_crawling,
            state="disabled"
        )
        self.pause_btn.pack(side="left", fill="x", expand=True, padx=(5, 5))
        
        self.stop_btn = ctk.CTkButton(
            self.controls_frame,
            text="⏹️ Stop",
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color="red",
            hover_color="darkred",
            command=self.stop_crawling,
            state="disabled"
        )
        self.stop_btn.pack(side="left", fill="x", expand=True, padx=(5, 0))
        
        # Additional Options
        self.options_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        self.options_frame.grid(row=13, column=0, padx=20, pady=10, sticky="ew", columnspan=2)
        
        self.follow_redirects = ctk.CTkCheckBox(self.options_frame, text="Follow Redirects")
        self.follow_redirects.select()
        self.follow_redirects.pack(anchor="w", pady=2)
        
        self.respect_robots = ctk.CTkCheckBox(self.options_frame, text="Respect robots.txt")
        self.respect_robots.select()
        self.respect_robots.pack(anchor="w", pady=2)
        
        self.use_selenium = ctk.CTkCheckBox(self.options_frame, text="Use Selenium (for JS sites)")
        self.use_selenium.pack(anchor="w", pady=2)
        
        # Settings Button
        self.settings_btn = ctk.CTkButton(
            self.sidebar,
            text="⚙️ Advanced Settings",
            height=35,
            command=self.open_settings
        )
        self.settings_btn.grid(row=14, column=0, padx=20, pady=(0, 20), sticky="ew", columnspan=2)
        
    def create_main_content(self):
        """Create the main content area"""
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.grid(row=0, column=1, sticky="nsew", padx=(0, 0), pady=(0, 0))
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(1, weight=1)
        
        # Tab View
        self.tabview = ctk.CTkTabview(self.main_frame)
        self.tabview.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        
        # Progress Tab
        self.progress_tab = self.tabview.add("📊 Progress")
        self.create_progress_tab()
        
        # Gallery Tab
        self.gallery_tab = self.tabview.add("🖼️ Gallery")
        self.create_gallery_tab()
        
        # Logs Tab
        self.logs_tab = self.tabview.add("📝 Logs")
        self.create_logs_tab()
        
        # Statistics Tab
        self.stats_tab = self.tabview.add("📈 Statistics")
        self.create_stats_tab()
        
    def create_progress_tab(self):
        """Create the progress monitoring tab"""
        self.progress_tab.grid_columnconfigure(0, weight=1)
        self.progress_tab.grid_rowconfigure(2, weight=1)
        
        # Progress Overview
        self.progress_overview = ctk.CTkFrame(self.progress_tab)
        self.progress_overview.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        
        # Stats Cards
        self.stats_frame = ctk.CTkFrame(self.progress_overview, fg_color="transparent")
        self.stats_frame.pack(fill="x", padx=10, pady=10)
        
        # Found Card
        self.found_card = self.create_stat_card(self.stats_frame, "Images Found", "0", "blue")
        self.found_card.pack(side="left", fill="x", expand=True, padx=(0, 5))
        
        # Downloaded Card
        self.downloaded_card = self.create_stat_card(self.stats_frame, "Downloaded", "0", "green")
        self.downloaded_card.pack(side="left", fill="x", expand=True, padx=(5, 5))
        
        # Failed Card
        self.failed_card = self.create_stat_card(self.stats_frame, "Failed", "0", "red")
        self.failed_card.pack(side="left", fill="x", expand=True, padx=(5, 5))
        
        # Speed Card
        self.speed_card = self.create_stat_card(self.stats_frame, "Speed", "0 img/s", "orange")
        self.speed_card.pack(side="left", fill="x", expand=True, padx=(5, 0))
        
        # Overall Progress Bar
        self.overall_progress_frame = ctk.CTkFrame(self.progress_tab)
        self.overall_progress_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=(0, 10))
        
        self.overall_label = ctk.CTkLabel(
            self.overall_progress_frame, 
            text="Overall Progress: 0%",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.overall_label.pack(pady=(10, 5))
        
        self.overall_progress = ctk.CTkProgressBar(self.overall_progress_frame, height=20)
        self.overall_progress.pack(fill="x", padx=20, pady=(0, 10))
        self.overall_progress.set(0)
        
        # Download Queue
        self.queue_frame = ctk.CTkFrame(self.progress_tab)
        self.queue_frame.grid(row=2, column=0, sticky="nsew", padx=10, pady=(0, 10))
        self.queue_frame.grid_columnconfigure(0, weight=1)
        self.queue_frame.grid_rowconfigure(1, weight=1)
        
        self.queue_label = ctk.CTkLabel(
            self.queue_frame,
            text="📥 Download Queue",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.queue_label.grid(row=0, column=0, sticky="w", padx=10, pady=10)
        
        # Create Treeview for queue
        self.queue_tree = ttk.Treeview(
            self.queue_frame,
            columns=("URL", "Status", "Size", "Type"),
            show="tree headings",
            height=15
        )
        
        # Configure columns
        self.queue_tree.column("#0", width=50, minwidth=50)
        self.queue_tree.column("URL", width=400, minwidth=200)
        self.queue_tree.column("Status", width=100, minwidth=80)
        self.queue_tree.column("Size", width=100, minwidth=80)
        self.queue_tree.column("Type", width=80, minwidth=60)
        
        # Configure headings
        self.queue_tree.heading("#0", text="#")
        self.queue_tree.heading("URL", text="Image URL")
        self.queue_tree.heading("Status", text="Status")
        self.queue_tree.heading("Size", text="Size")
        self.queue_tree.heading("Type", text="Type")
        
        # Create scrollbar
        self.queue_scroll = ttk.Scrollbar(self.queue_frame, orient="vertical", command=self.queue_tree.yview)
        self.queue_tree.configure(yscrollcommand=self.queue_scroll.set)
        
        # Grid treeview and scrollbar
        self.queue_tree.grid(row=1, column=0, sticky="nsew", padx=(10, 0), pady=(0, 10))
        self.queue_scroll.grid(row=1, column=1, sticky="ns", padx=(0, 10), pady=(0, 10))
        
    def create_gallery_tab(self):
        """Create the image gallery tab"""
        self.gallery_tab.grid_columnconfigure(0, weight=1)
        self.gallery_tab.grid_rowconfigure(1, weight=1)
        
        # Gallery Controls
        self.gallery_controls = ctk.CTkFrame(self.gallery_tab, height=50)
        self.gallery_controls.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        
        self.refresh_gallery_btn = ctk.CTkButton(
            self.gallery_controls,
            text="🔄 Refresh",
            width=100,
            command=self.refresh_gallery
        )
        self.refresh_gallery_btn.pack(side="left", padx=(10, 5))
        
        self.open_folder_btn = ctk.CTkButton(
            self.gallery_controls,
            text="📁 Open Folder",
            width=120,
            command=self.open_download_folder
        )
        self.open_folder_btn.pack(side="left", padx=5)
        
        self.view_size_slider = ctk.CTkSlider(
            self.gallery_controls,
            from_=100,
            to=300,
            width=200,
            command=self.update_thumbnail_size
        )
        self.view_size_slider.set(150)
        self.view_size_slider.pack(side="right", padx=(5, 10))
        
        self.size_label = ctk.CTkLabel(self.gallery_controls, text="Thumbnail Size:")
        self.size_label.pack(side="right", padx=(10, 5))

        # Gallery statistics label
        self.gallery_stats_label = ctk.CTkLabel(
            self.gallery_controls,
            text="Gallery: 0 images",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        self.gallery_stats_label.pack(side="right", padx=(20, 10))
        
        # Gallery Scrollable Frame
        self.gallery_scroll = ctk.CTkScrollableFrame(self.gallery_tab)
        self.gallery_scroll.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))

        # Initialize lazy loading gallery
        self.thumbnail_size = 150
        self.lazy_gallery = LazyGallery(
            self.gallery_scroll,
            thumbnail_size=self.thumbnail_size,
            viewport_buffer=2,
            max_concurrent_loads=3
        )
        self.lazy_gallery.set_click_callback(self.view_image)

        # Initialize image cache
        self.image_cache = get_image_cache()

        # Keep reference for backward compatibility
        self.gallery_images = []
        
    def create_logs_tab(self):
        """Create the logs tab"""
        self.logs_tab.grid_columnconfigure(0, weight=1)
        self.logs_tab.grid_rowconfigure(1, weight=1)
        
        # Log Controls
        self.log_controls = ctk.CTkFrame(self.logs_tab, height=50)
        self.log_controls.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        
        self.clear_logs_btn = ctk.CTkButton(
            self.log_controls,
            text="🗑️ Clear Logs",
            width=100,
            command=self.clear_logs
        )
        self.clear_logs_btn.pack(side="left", padx=(10, 5))
        
        self.export_logs_btn = ctk.CTkButton(
            self.log_controls,
            text="💾 Export Logs",
            width=100,
            command=self.export_logs
        )
        self.export_logs_btn.pack(side="left", padx=5)
        
        # Log Level Filter
        self.log_level_var = ctk.StringVar(value="ALL")
        self.log_level_menu = ctk.CTkOptionMenu(
            self.log_controls,
            values=["ALL", "INFO", "WARNING", "ERROR"],
            variable=self.log_level_var,
            command=self.filter_logs
        )
        self.log_level_menu.pack(side="right", padx=(5, 10))
        
        self.log_level_label = ctk.CTkLabel(self.log_controls, text="Log Level:")
        self.log_level_label.pack(side="right", padx=(10, 5))
        
        # Log Text Widget
        self.log_text = ctk.CTkTextbox(self.logs_tab, wrap="word")
        self.log_text.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))
        
        # Configure tags for different log levels
        self.log_text.tag_config("INFO", foreground="lightblue")
        self.log_text.tag_config("WARNING", foreground="yellow")
        self.log_text.tag_config("ERROR", foreground="red")
        self.log_text.tag_config("SUCCESS", foreground="lightgreen")
        
    def create_stats_tab(self):
        """Create the statistics tab"""
        self.stats_tab.grid_columnconfigure(0, weight=1)
        self.stats_tab.grid_columnconfigure(1, weight=1)
        self.stats_tab.grid_rowconfigure(1, weight=1)
        
        # Summary Stats
        self.summary_frame = ctk.CTkFrame(self.stats_tab)
        self.summary_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=10, pady=10)
        
        self.summary_label = ctk.CTkLabel(
            self.summary_frame,
            text="📊 Crawl Statistics",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        self.summary_label.pack(pady=10)
        
        # Detailed Stats
        self.detailed_stats = ctk.CTkFrame(self.stats_tab)
        self.detailed_stats.grid(row=1, column=0, sticky="nsew", padx=(10, 5), pady=(0, 10))
        
        self.create_stat_item(self.detailed_stats, "Total URLs Crawled:", "0")
        self.create_stat_item(self.detailed_stats, "Total Images Found:", "0")
        self.create_stat_item(self.detailed_stats, "Images Downloaded:", "0")
        self.create_stat_item(self.detailed_stats, "Images Skipped:", "0")
        self.create_stat_item(self.detailed_stats, "Download Errors:", "0")
        self.create_stat_item(self.detailed_stats, "Average Image Size:", "0 KB")
        self.create_stat_item(self.detailed_stats, "Total Download Size:", "0 MB")
        self.create_stat_item(self.detailed_stats, "Crawl Duration:", "00:00:00")
        
        # File Type Distribution
        self.distribution_frame = ctk.CTkFrame(self.stats_tab)
        self.distribution_frame.grid(row=1, column=1, sticky="nsew", padx=(5, 10), pady=(0, 10))
        
        self.dist_label = ctk.CTkLabel(
            self.distribution_frame,
            text="📁 File Type Distribution",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.dist_label.pack(pady=10)
        
        self.file_types_frame = ctk.CTkFrame(self.distribution_frame)
        self.file_types_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
    def create_status_bar(self):
        """Create the status bar at the bottom"""
        self.status_bar = ctk.CTkFrame(self, height=30)
        self.status_bar.grid(row=1, column=0, columnspan=2, sticky="ew")
        
        self.status_label = ctk.CTkLabel(self.status_bar, text="Ready to crawl")
        self.status_label.pack(side="left", padx=10)
        
        self.connection_label = ctk.CTkLabel(self.status_bar, text="🟢 Connected")
        self.connection_label.pack(side="right", padx=10)
        
    def create_stat_card(self, parent, title, value, color):
        """Create a statistics card widget"""
        print('DEBUG: create_stat_card self type:', type(self))
        print('DEBUG: create_stat_card self MRO:', type(self).mro())
        card = ctk.CTkFrame(parent, fg_color=color)
        title_label = ctk.CTkLabel(card, text=title, font=ctk.CTkFont(size=12))
        title_label.pack(pady=(10, 5))
        value_label = ctk.CTkLabel(card, text=value, font=ctk.CTkFont(size=20, weight="bold"))
        value_label.pack(pady=(0, 10))
        # Store reference to value label for updates
        self.stat_value_labels[title] = value_label
        return card
        
    def create_stat_item(self, parent, label, value):
        """Create a statistic item"""
        frame = ctk.CTkFrame(parent, fg_color="transparent")
        frame.pack(fill="x", padx=20, pady=5)
        label_widget = ctk.CTkLabel(frame, text=label, font=ctk.CTkFont(size=14))
        label_widget.pack(side="left")
        value_widget = ctk.CTkLabel(frame, text=value, font=ctk.CTkFont(size=14, weight="bold"))
        value_widget.pack(side="right")
        # Store reference for updates
        self.stat_item_value_widgets[label] = value_widget
        return frame
        
    def bind_events(self):
        """Bind keyboard events"""
        self.bind("<Control-s>", lambda e: self.start_crawling())
        self.bind("<Control-q>", lambda e: self.quit())
        self.url_entry.bind("<Return>", lambda e: self.start_crawling())

    def setup_input_validation(self):
        """Setup real-time input validation"""
        # URL validation
        self.url_entry.bind("<FocusOut>", self.validate_url_input)
        self.url_entry.bind("<KeyRelease>", self.validate_url_input)

        # Directory validation
        self.dir_entry.bind("<FocusOut>", self.validate_directory_input)

        # Numeric validation
        self.max_images_entry.bind("<FocusOut>", self.validate_max_images)
        self.min_width_entry.bind("<FocusOut>", self.validate_min_width)
        self.min_height_entry.bind("<FocusOut>", self.validate_min_height)

        # File types validation
        self.types_entry.bind("<FocusOut>", self.validate_file_types)

    def validate_url_input(self, event=None):
        """Validate URL input in real-time"""
        url = self.url_entry.get().strip()
        if url:  # Only validate if not empty
            is_valid, error, suggestion = InputValidator.validate_url(url)
            if not is_valid:
                self.url_entry.configure(border_color="red")
                # Could show tooltip or status message here
            else:
                self.url_entry.configure(border_color="gray")

    def validate_directory_input(self, event=None):
        """Validate directory input"""
        path = self.dir_entry.get().strip()
        if path:
            is_valid, error, suggestion = InputValidator.validate_directory_path(path)
            if not is_valid:
                self.dir_entry.configure(border_color="red")
            else:
                self.dir_entry.configure(border_color="gray")

    def validate_max_images(self, event=None):
        """Validate max images input"""
        value = self.max_images_entry.get().strip()
        if value:
            is_valid, parsed_val, error, suggestion = InputValidator.validate_numeric_input(
                value, 1, 10000, int, "Max Images"
            )
            if not is_valid:
                self.max_images_entry.configure(border_color="red")
            else:
                self.max_images_entry.configure(border_color="gray")

    def validate_min_width(self, event=None):
        """Validate min width input"""
        value = self.min_width_entry.get().strip()
        if value:
            is_valid, parsed_val, error, suggestion = InputValidator.validate_numeric_input(
                value, 1, 10000, int, "Min Width"
            )
            if not is_valid:
                self.min_width_entry.configure(border_color="red")
            else:
                self.min_width_entry.configure(border_color="gray")

    def validate_min_height(self, event=None):
        """Validate min height input"""
        value = self.min_height_entry.get().strip()
        if value:
            is_valid, parsed_val, error, suggestion = InputValidator.validate_numeric_input(
                value, 1, 10000, int, "Min Height"
            )
            if not is_valid:
                self.min_height_entry.configure(border_color="red")
            else:
                self.min_height_entry.configure(border_color="gray")

    def validate_file_types(self, event=None):
        """Validate file types input"""
        value = self.types_entry.get().strip()
        if value:
            is_valid, parsed_types, error, suggestion = InputValidator.validate_file_types(value)
            if not is_valid:
                self.types_entry.configure(border_color="red")
            else:
                self.types_entry.configure(border_color="gray")
        
    def browse_directory(self):
        """Open directory browser"""
        directory = filedialog.askdirectory(
            initialdir=self.dir_entry.get(),
            title="Select Download Directory"
        )
        if directory:
            self.dir_entry.delete(0, tk.END)
            self.dir_entry.insert(0, directory)
            
    def start_crawling(self):
        """Start the crawling process"""
        # Validate all inputs using InputValidator
        settings = {
            'url': self.url_entry.get().strip(),
            'download_dir': self.dir_entry.get().strip(),
            'max_depth': self.depth_slider.get(),
            'max_images': self.max_images_entry.get(),
            'concurrent_downloads': self.concurrent_slider.get(),
            'file_types': self.types_entry.get()
        }

        # Add min size settings if enabled
        if self.min_size_check.get():
            settings['min_width'] = self.min_width_entry.get()
            settings['min_height'] = self.min_height_entry.get()

        # Validate all settings
        is_valid, errors = InputValidator.validate_crawler_settings(settings)

        if not is_valid:
            # Show first error found
            first_error = next(iter(errors.values()))
            self.show_error_message(first_error['error'], first_error['suggestion'])
            return

        # Create download directory if it doesn't exist
        try:
            os.makedirs(settings['download_dir'], exist_ok=True)
        except Exception as e:
            self.show_error_message(f"Failed to create download directory: {str(e)}", "Check folder permissions or run as administrator.")
            return
            
        # Update UI state
        self.is_crawling = True
        self.is_paused = False
        self.start_btn.configure(state="disabled")
        self.pause_btn.configure(state="normal")
        self.stop_btn.configure(state="normal")
        self.url_entry.configure(state="disabled")
        
        # Reset statistics
        self.reset_statistics()
        
        # Log start
        self.log("INFO", f"Starting crawl of: {settings['url']}")
        self.status_label.configure(text=f"Crawling: {settings['url']}")
        
        # Parse and validate file types
        is_valid, file_types, error, suggestion = InputValidator.validate_file_types(settings['file_types'])
        if not is_valid:
            self.show_error_message(error, suggestion)
            return

        # Create crawler instance with validated settings
        crawler_config = {
            'url': settings['url'],
            'download_dir': settings['download_dir'],
            'max_depth': int(settings['max_depth']),
            'max_images': int(settings['max_images']),
            'concurrent_downloads': int(settings['concurrent_downloads']),
            'file_types': file_types,
            'min_width': int(settings.get('min_width', 0)) if self.min_size_check.get() else None,
            'min_height': int(settings.get('min_height', 0)) if self.min_size_check.get() else None,
            'follow_redirects': self.follow_redirects.get(),
            'respect_robots': self.respect_robots.get(),
            'use_selenium': self.use_selenium.get(),
            'callback': self.crawler_callback,
            'retry_attempts': self.settings.get('retry_attempts', 3)
        }
        
        self.crawler = ImageCrawler(**crawler_config)
        
        # Start crawler in separate thread
        self.crawler_thread = threading.Thread(target=self.crawler.start, daemon=True)
        self.crawler_thread.start()
        
    def pause_crawling(self):
        """Pause/Resume crawling"""
        if self.crawler:
            if self.is_paused:
                self.crawler.resume()
                self.pause_btn.configure(text="⏸️ Pause")
                self.is_paused = False
                self.log("INFO", "Crawling resumed")
            else:
                self.crawler.pause()
                self.pause_btn.configure(text="▶️ Resume")
                self.is_paused = True
                self.log("INFO", "Crawling paused")
                
    def stop_crawling(self):
        """Stop the crawling process"""
        if self.crawler:
            self.crawler.stop()
            self.log("WARNING", "Crawling stopped by user")
            
        self.is_crawling = False
        self.is_paused = False
        self.start_btn.configure(state="normal")
        self.pause_btn.configure(state="disabled", text="⏸️ Pause")
        self.stop_btn.configure(state="disabled")
        self.url_entry.configure(state="normal")
        self.status_label.configure(text="Crawl stopped")
        
    def crawler_callback(self, event_type, data):
        """Handle callbacks from crawler"""
        self.download_queue.put((event_type, data))
        
    def process_queue(self):
        """Process events from crawler queue with optimized batching"""
        if not self.queue_processing_active:
            return

        events_processed = 0
        max_events_per_cycle = 10  # Process up to 10 events per cycle

        try:
            while events_processed < max_events_per_cycle:
                event_type, data = self.download_queue.get_nowait()

                if event_type == 'image_found':
                    self.handle_image_found(data)
                elif event_type == 'image_downloaded':
                    self.handle_image_downloaded(data)
                elif event_type == 'image_failed':
                    self.handle_image_failed(data)
                elif event_type == 'crawl_complete':
                    self.handle_crawl_complete(data)
                elif event_type == 'progress_update':
                    self.handle_progress_update(data)
                elif event_type == 'log':
                    self.log(data['level'], data['message'])

                events_processed += 1

        except queue.Empty:
            pass

        # Adaptive scheduling based on queue size
        try:
            queue_size = self.download_queue.qsize()
            if queue_size > 20:
                # High load - process more frequently
                next_check = 25
            elif queue_size > 5:
                # Medium load - normal frequency
                next_check = 50
            else:
                # Low load - less frequent checks
                next_check = 100
        except:
            next_check = 50

        # Schedule next check
        if self.queue_processing_active:
            self.after(next_check, self.process_queue)
        
    def handle_image_found(self, data):
        """Handle when a new image is found"""
        self.total_images_found += 1
        self.stat_value_labels["Images Found"].configure(text=str(self.total_images_found))
        
        # Add to queue tree
        item = self.queue_tree.insert(
            "", 
            "end", 
            text=str(self.total_images_found),
            values=(data['url'], "Pending", "-", data.get('type', 'Unknown'))
        )
        data['tree_item'] = item
        
    def handle_image_downloaded(self, data):
        """Handle when an image is successfully downloaded"""
        self.images_downloaded += 1
        self.stat_value_labels["Downloaded"].configure(text=str(self.images_downloaded))
        
        # Update queue tree
        if 'tree_item' in data:
            self.queue_tree.item(
                data['tree_item'],
                values=(data['url'], "✓ Downloaded", data.get('size', '-'), data.get('type', '-'))
            )
            
        # Add to gallery
        self.add_to_gallery(data['path'])
        
        # Log success
        self.log("SUCCESS", f"Downloaded: {os.path.basename(data['path'])}")
        
    def handle_image_failed(self, data):
        """Handle when an image fails to download"""
        self.images_failed += 1
        self.stat_value_labels["Failed"].configure(text=str(self.images_failed))
        
        # Update queue tree
        if 'tree_item' in data:
            self.queue_tree.item(
                data['tree_item'],
                values=(data['url'], "✗ Failed", "-", data.get('type', '-'))
            )
            
        # Log error
        self.log("ERROR", f"Failed to download: {data['url']} - {data.get('error', 'Unknown error')}")
        
    def handle_crawl_complete(self, data):
        """Handle when crawling is complete"""
        self.is_crawling = False
        self.start_btn.configure(state="normal")
        self.pause_btn.configure(state="disabled")
        self.stop_btn.configure(state="disabled")
        self.url_entry.configure(state="normal")
        
        self.status_label.configure(text="Crawl complete")
        self.log("SUCCESS", f"Crawl complete! Downloaded {self.images_downloaded} images")
        
        # Show completion message
        messagebox.showinfo(
            "Crawl Complete",
            f"Successfully downloaded {self.images_downloaded} images\n"
            f"Failed: {self.images_failed}\n"
            f"Total found: {self.total_images_found}"
        )
        
    def handle_progress_update(self, data):
        """Update overall progress"""
        progress = data.get('progress', 0)
        self.overall_progress.set(progress / 100)
        self.overall_label.configure(text=f"Overall Progress: {progress}%")
        
        # Update speed
        speed = data.get('speed', 0)
        self.stat_value_labels["Speed"].configure(text=f"{speed:.1f} img/s")
        
    def add_to_gallery(self, image_path):
        """Add an image to the gallery using lazy loading"""
        try:
            # Check if file still exists
            if not os.path.exists(image_path):
                return

            # Add to lazy gallery
            self.lazy_gallery.add_image(image_path)

            # Keep backward compatibility by adding to gallery_images list
            self.gallery_images.append({
                'path': image_path,
                'loaded': False  # Track loading state
            })

        except Exception as e:
            self.log("ERROR", f"Failed to add image to gallery: {str(e)}")
            
    def view_image(self, image_path):
        """Open image viewer for selected image"""
        viewer = ImageViewer(self, image_path)
        
    def update_thumbnail_size(self, value):
        """Update gallery thumbnail size with lazy loading"""
        self.thumbnail_size = int(value)
        self.lazy_gallery.update_thumbnail_size(self.thumbnail_size)

    def update_gallery_statistics(self):
        """Update gallery statistics display"""
        try:
            if hasattr(self, 'lazy_gallery') and hasattr(self, 'gallery_stats_label'):
                stats = self.lazy_gallery.get_statistics()

                # Format statistics text
                total = stats['total_images']
                loaded = stats['loaded_images']
                loading = stats['loading_images']

                if total == 0:
                    stats_text = "Gallery: 0 images"
                else:
                    stats_text = f"Gallery: {total} images ({loaded} loaded"
                    if loading > 0:
                        stats_text += f", {loading} loading"
                    stats_text += f", {stats['loading_percentage']:.0f}%)"

                self.gallery_stats_label.configure(text=stats_text)

        except Exception as e:
            # Silently handle errors to avoid disrupting the UI
            pass

        # Schedule next update
        if self.queue_processing_active:
            self.after(1000, self.update_gallery_statistics)
        
    def refresh_gallery(self):
        """Refresh the gallery with downloaded images using lazy loading"""
        # Clear existing gallery
        self.lazy_gallery.clear_gallery()
        self.gallery_images.clear()

        # Force garbage collection
        import gc
        gc.collect()

        # Load images from download directory
        download_dir = self.dir_entry.get()
        if os.path.exists(download_dir):
            image_files = []
            for filename in os.listdir(download_dir):
                if any(filename.lower().endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']):
                    image_files.append(os.path.join(download_dir, filename))

            # Sort files by modification time (newest first)
            image_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

            # With lazy loading, we can load all images without memory concerns
            # Only visible ones will actually be loaded into memory
            self.lazy_gallery.refresh_with_images(image_files)

            # Update gallery_images for backward compatibility
            for image_path in image_files:
                self.gallery_images.append({
                    'path': image_path,
                    'loaded': False
                })
                    
    def open_download_folder(self):
        """Open the download folder in file explorer"""
        download_dir = self.dir_entry.get()
        if os.path.exists(download_dir):
            if sys.platform == 'win32':
                os.startfile(download_dir)
            elif sys.platform == 'darwin':
                os.system(f'open "{download_dir}"')
            else:
                os.system(f'xdg-open "{download_dir}"')
                
    def clear_logs(self):
        """Clear the log text"""
        self.log_text.delete("1.0", tk.END)
        
    def export_logs(self):
        """Export logs to file"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w') as f:
                    f.write(self.log_text.get("1.0", tk.END))
                messagebox.showinfo("Success", "Logs exported successfully")
            except Exception as e:
                self.show_error_message(f"Failed to export logs: {str(e)}", "Check disk space or file permissions.")
                
    def filter_logs(self, level):
        """Filter logs by level"""
        # This is a simplified implementation
        # In a real app, you'd store all logs and filter them
        pass
        
    def show_error_message(self, message, suggestion=None):
        full_message = message
        if suggestion:
            full_message += f"\n\nSuggestion: {suggestion}"
        messagebox.showerror("Error", full_message)

    def log(self, level, message):
        """Add a log entry"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        self.log_text.insert(tk.END, log_entry, level)
        self.log_text.see(tk.END)
        # Show error dialog for ERROR or higher
        if level in ("ERROR", "CRITICAL"):  # Add CRITICAL if used
            suggestion = None
            if "network" in message.lower() or "timeout" in message.lower():
                suggestion = "Check your internet connection or try again later."
            elif "permission" in message.lower() or "access" in message.lower():
                suggestion = "Check folder permissions or run as administrator."
            elif "disk" in message.lower() or "space" in message.lower():
                suggestion = "Check available disk space."
            self.show_error_message(message, suggestion)
        
    def reset_statistics(self):
        """Reset all statistics"""
        self.total_images_found = 0
        self.images_downloaded = 0
        self.images_failed = 0
        
        # Update UI
        self.stat_value_labels["Images Found"].configure(text="0")
        self.stat_value_labels["Downloaded"].configure(text="0")
        self.stat_value_labels["Failed"].configure(text="0")
        self.stat_value_labels["Speed"].configure(text="0 img/s")
        self.overall_progress.set(0)
        self.overall_label.configure(text="Overall Progress: 0%")
        
        # Clear queue tree
        for item in self.queue_tree.get_children():
            self.queue_tree.delete(item)
            
    def open_settings(self):
        """Open advanced settings dialog"""
        settings_window = ctk.CTkToplevel(self)
        settings_window.title("Advanced Settings")
        settings_window.geometry("700x600")
        settings_window.transient(self)

        # Create settings UI
        settings_label = ctk.CTkLabel(
            settings_window,
            text="Advanced Settings",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        settings_label.pack(pady=20)

        # Cache Management Section
        cache_frame = ctk.CTkFrame(settings_window)
        cache_frame.pack(fill="x", padx=20, pady=10)

        cache_title = ctk.CTkLabel(cache_frame, text="Image Cache Management", font=ctk.CTkFont(size=16, weight="bold"))
        cache_title.pack(pady=10)

        # Cache statistics
        cache_stats = self.image_cache.get_cache_stats()
        stats_text = f"""Cache Statistics:
• Disk Usage: {cache_stats['disk_usage_mb']:.1f} MB
• Thumbnails: {cache_stats['thumbnail_cache']['size']}/{cache_stats['thumbnail_cache']['max_size']} ({cache_stats['thumbnail_cache']['hit_rate']:.1f}% hit rate)
• Images: {cache_stats['image_cache']['size']}/{cache_stats['image_cache']['max_size']} ({cache_stats['image_cache']['hit_rate']:.1f}% hit rate)
• Memory Usage: {cache_stats['thumbnail_cache']['memory_mb'] + cache_stats['image_cache']['memory_mb']:.1f} MB"""

        cache_stats_label = ctk.CTkLabel(cache_frame, text=stats_text, font=ctk.CTkFont(size=12), justify="left")
        cache_stats_label.pack(pady=10)

        # Cache control buttons
        cache_buttons_frame = ctk.CTkFrame(cache_frame, fg_color="transparent")
        cache_buttons_frame.pack(pady=10)

        clear_thumbnails_btn = ctk.CTkButton(
            cache_buttons_frame,
            text="Clear Thumbnails",
            command=lambda: self.clear_cache("thumbnails", settings_window)
        )
        clear_thumbnails_btn.pack(side="left", padx=5)

        clear_images_btn = ctk.CTkButton(
            cache_buttons_frame,
            text="Clear Images",
            command=lambda: self.clear_cache("images", settings_window)
        )
        clear_images_btn.pack(side="left", padx=5)

        clear_all_cache_btn = ctk.CTkButton(
            cache_buttons_frame,
            text="Clear All Cache",
            command=lambda: self.clear_cache(None, settings_window)
        )
        clear_all_cache_btn.pack(side="left", padx=5)

        # Cache settings
        cache_settings_frame = ctk.CTkFrame(cache_frame, fg_color="transparent")
        cache_settings_frame.pack(pady=10)

        cache_enabled_label = ctk.CTkLabel(cache_settings_frame, text="Enable Caching:")
        cache_enabled_label.pack(side="left", padx=5)

        cache_enabled_switch = ctk.CTkSwitch(
            cache_settings_frame,
            text="",
            command=self.toggle_cache
        )
        cache_enabled_switch.pack(side="left", padx=5)
        if cache_stats['enabled']:
            cache_enabled_switch.select()

        close_btn = ctk.CTkButton(
            settings_window,
            text="Close",
            command=settings_window.destroy
        )
        close_btn.pack(pady=20)

    def clear_cache(self, cache_type=None, parent_window=None):
        """Clear image cache"""
        try:
            if cache_type:
                self.image_cache.clear_cache(cache_type)
                message = f"{cache_type.title()} cache cleared successfully!"
            else:
                self.image_cache.clear_cache()
                message = "All cache cleared successfully!"

            self.log("INFO", message)

            if parent_window:
                messagebox.showinfo("Cache Cleared", message, parent=parent_window)
            else:
                messagebox.showinfo("Cache Cleared", message)

            # Refresh gallery to reload images
            self.refresh_gallery()

        except Exception as e:
            error_msg = f"Failed to clear cache: {str(e)}"
            self.log("ERROR", error_msg)
            if parent_window:
                messagebox.showerror("Error", error_msg, parent=parent_window)
            else:
                messagebox.showerror("Error", error_msg)

    def toggle_cache(self):
        """Toggle cache enabled/disabled"""
        try:
            current_state = self.image_cache.cache_enabled
            self.image_cache.set_enabled(not current_state)

            status = "enabled" if not current_state else "disabled"
            message = f"Image caching {status}"
            self.log("INFO", message)

        except Exception as e:
            error_msg = f"Failed to toggle cache: {str(e)}"
            self.log("ERROR", error_msg)

    def on_closing(self):
        """Handle window closing with proper cleanup"""
        if self.is_crawling:
            if messagebox.askokcancel("Quit", "Crawling is in progress. Do you want to stop and quit?"):
                if self.crawler:
                    self.crawler.stop()
                self.cleanup_resources()
                self.destroy()
        else:
            self.cleanup_resources()
            self.destroy()

    def cleanup_resources(self):
        """Clean up resources before closing"""
        try:
            # Stop queue processing
            self.queue_processing_active = False

            # Clean up lazy gallery
            if hasattr(self, 'lazy_gallery'):
                self.lazy_gallery.clear_gallery()

            # Clean up gallery images list
            self.gallery_images.clear()

            # Force garbage collection
            import gc
            gc.collect()

        except Exception as e:
            print(f"Error during cleanup: {e}")

    def thread_exception_handler(self, args):
        """Global handler for uncaught exceptions in threads."""
        try:
            exc_type = args.exc_type.__name__ if args.exc_type else 'Exception'
            exc_msg = str(args.exc_value) if args.exc_value else 'Unknown error'
            tb = args.exc_traceback

            import traceback
            tb_str = ''.join(traceback.format_tb(tb)) if tb else ''
            log_msg = f"Uncaught thread exception: {exc_type}: {exc_msg}\n{tb_str}"

            # Schedule GUI update on main thread
            self.after(0, lambda: self._handle_thread_exception(exc_type, exc_msg, log_msg))
        except Exception as e:
            # Fallback error handling
            print(f"Error in thread exception handler: {e}")

    def _handle_thread_exception(self, exc_type, exc_msg, log_msg):
        """Handle thread exception on main GUI thread"""
        try:
            self.log('ERROR', log_msg)
            messagebox.showerror("Thread Exception", f"{exc_type}: {exc_msg}\n\nSee logs for details.")
        except Exception as e:
            print(f"Error displaying thread exception: {e}")

    def settings_error_callback(self, level, msg, suggestion=None):
        if suggestion:
            self.show_error_message(msg, suggestion)
        else:
            self.log(level, msg)

# --- Main block: always instantiate and run the app this way ---
if __name__ == "__main__":
    app = ImageCrawlerApp()
    app.protocol("WM_DELETE_WINDOW", app.on_closing)
    app.mainloop()
